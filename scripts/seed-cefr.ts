
import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import csv from 'csv-parser';

const prisma = new PrismaClient();

async function main() {
  const results: { headword: string; CEFR: string }[] = [];

  fs.createReadStream('scripts/cefr-data-processors/ENGLISH_CERF_WORDS.csv')
    .pipe(csv())
    .on('data', (data) => results.push(data))
    .on('end', async () => {
      const cefrWords = results.map((row) => ({
        term: row.headword,
        level: row.CEFR,
      }));

      // Using createMany for bulk insertion
      try {
        const result = await prisma.cefrWord.createMany({
          data: cefrWords,
          skipDuplicates: true, // Skip if a word with the same term already exists
        });
        console.log(`Successfully seeded ${result.count} CEFR words.`);
      } catch (error) {
        console.error('Error seeding CEFR words:', error);
      } finally {
        await prisma.$disconnect();
      }
    });
}

main().catch((e) => {
  console.error(e);
  prisma.$disconnect();
  process.exit(1);
});
