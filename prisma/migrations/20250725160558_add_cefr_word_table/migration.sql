-- CreateTable
CREATE TABLE "CefrWord" (
    "id" TEXT NOT NULL,
    "term" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CefrWord_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CefrWord_term_idx" ON "CefrWord"("term");

-- CreateIndex
CREATE INDEX "CefrWord_level_idx" ON "CefrWord"("level");

-- CreateIndex
CREATE UNIQUE INDEX "CefrWord_term_key" ON "CefrWord"("term");
