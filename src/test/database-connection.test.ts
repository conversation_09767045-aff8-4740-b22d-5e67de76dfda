import { getPrismaClient } from '@/backend/wire';
import { getDatabaseConfig } from '@/config/database.config';

describe('Database Connection Pool', () => {
	let prismaClient: ReturnType<typeof getPrismaClient>;

	beforeAll(() => {
		prismaClient = getPrismaClient();
	});

	afterAll(async () => {
		await prismaClient.$disconnect();
	});

	it('should create a single PrismaClient instance', () => {
		const client1 = getPrismaClient();
		const client2 = getPrismaClient();
		
		// Should return the same instance (singleton pattern)
		expect(client1).toBe(client2);
	});

	it('should have proper database configuration', () => {
		const config = getDatabaseConfig();
		
		expect(config).toHaveProperty('connectionLimit');
		expect(config).toHaveProperty('connectTimeout');
		expect(config).toHaveProperty('queryTimeout');
		expect(config).toHaveProperty('poolTimeout');
		expect(config).toHaveProperty('logLevel');
		
		// Connection limit should be appropriate for environment
		if (process.env.NODE_ENV === 'development') {
			expect(config.connectionLimit).toBe(5);
		} else {
			expect(config.connectionLimit).toBeGreaterThan(5);
		}
	});

	it('should be able to connect to database', async () => {
		// This should not throw an error
		await expect(prismaClient.$queryRaw`SELECT 1 as test`).resolves.toEqual([{ test: 1 }]);
	});

	it('should handle multiple concurrent connections without exhaustion', async () => {
		// Create multiple concurrent database operations
		const operations = Array.from({ length: 10 }, (_, i) => 
			prismaClient.$queryRaw`SELECT ${i} as operation_id`
		);

		// All operations should complete successfully without connection pool exhaustion
		const results = await Promise.all(operations);
		
		expect(results).toHaveLength(10);
		results.forEach((result, index) => {
			expect(result).toEqual([{ operation_id: index }]);
		});
	});

	it('should properly configure connection URL with pool parameters', () => {
		const config = getDatabaseConfig();
		
		// URL should contain connection limit parameter
		expect(config.url).toContain('connection_limit');
		expect(config.url).toContain('pool_timeout');
		expect(config.url).toContain('schema=public');
	});
});
