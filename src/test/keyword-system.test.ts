/**
 * Test file for keyword system with IndexedDB and background sync
 */

import { keywordStorage, KeywordStorage } from '@/lib/keyword-storage';
import { KeywordWithDetail } from '@/models';
import { resetSharedDBManager, deleteIndexedDB } from '@/lib/indexed-db/indexed-db';

// Mock localStorage for testing
const localStorageMock = (() => {
	let store: Record<string, string> = {};

	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value.toString();
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
	};
})();

// Mock IndexedDB for testing
const indexedDBMock = {
	open: jest.fn(),
	deleteDatabase: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
	value: localStorageMock,
});

Object.defineProperty(window, 'indexedDB', {
	value: indexedDBMock,
});

describe('Keyword Storage System', () => {
	beforeEach(async () => {
		localStorageMock.clear();
		resetSharedDBManager();

		// Mock IndexedDB operations
		indexedDBMock.open.mockImplementation(() => {
			const request = {
				onsuccess: null as any,
				onerror: null as any,
				onupgradeneeded: null as any,
				result: {
					objectStoreNames: { contains: () => false },
					createObjectStore: jest.fn(),
					transaction: () => ({
						objectStore: () => ({
							put: jest.fn(),
							get: jest.fn(),
							getAll: jest.fn(),
							delete: jest.fn(),
							clear: jest.fn(),
						}),
					}),
				},
			};

			// Simulate successful database opening
			setTimeout(() => {
				if (request.onsuccess) {
					request.onsuccess({ target: request });
				}
			}, 0);

			return request;
		});
	});

	test('should save and retrieve keywords from IndexedDB', async () => {
		const testKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
			{
				id: '2',
				content: 'business',
				user_id: 'user1',
			},
		];

		await keywordStorage.saveKeywords(testKeywords);
		const retrievedKeywords = await keywordStorage.getKeywords();

		expect(retrievedKeywords).toEqual(testKeywords);
	});

	test('should save and retrieve selected keywords', async () => {
		const selectedIds = ['1', '2', '3'];

		await keywordStorage.saveSelectedKeywords(selectedIds);
		const retrievedSelected = await keywordStorage.getSelectedKeywords();

		expect(retrievedSelected).toEqual(selectedIds);
	});

	test('should add keyword locally with optimistic update', async () => {
		const newKeyword: KeywordWithDetail = {
			id: '3',
			content: 'science',
			user_id: 'user1',
		};

		await keywordStorage.addKeywordLocally(newKeyword);
		const keywords = await keywordStorage.getKeywords();

		expect(keywords).toContain(newKeyword);
	});

	test('should update keyword locally', async () => {
		const initialKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
		];

		await keywordStorage.saveKeywords(initialKeywords);
		await keywordStorage.updateKeywordLocally('1', { content: 'updated-technology' });

		const keywords = await keywordStorage.getKeywords();
		expect(keywords[0].content).toBe('updated-technology');
	});

	test('should delete keyword locally', async () => {
		const initialKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
			{
				id: '2',
				content: 'business',
				user_id: 'user1',
			},
		];

		await keywordStorage.saveKeywords(initialKeywords);
		await keywordStorage.saveSelectedKeywords(['1', '2']);

		await keywordStorage.deleteKeywordLocally('1');

		const keywords = await keywordStorage.getKeywords();
		const selectedKeywords = await keywordStorage.getSelectedKeywords();

		expect(keywords).toHaveLength(1);
		expect(keywords[0].id).toBe('2');
		expect(selectedKeywords).toEqual(['2']);
	});

	test('should manage sync queue', async () => {
		const action = {
			id: 'action1',
			type: 'create' as const,
			data: { name: 'test-keyword' },
		};

		await keywordStorage.addToSyncQueue(action);
		const queue = await keywordStorage.getSyncQueue();

		expect(queue).toHaveLength(1);
		expect(queue[0].id).toBe('action1');
		expect(queue[0].type).toBe('create');
		expect(queue[0].timestamp).toBeDefined();

		await keywordStorage.removeFromSyncQueue('action1');
		const updatedQueue = await keywordStorage.getSyncQueue();

		expect(updatedQueue).toHaveLength(0);
	});

	test('should clear all data', async () => {
		const testKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
		];

		await keywordStorage.saveKeywords(testKeywords);
		await keywordStorage.saveSelectedKeywords(['1']);
		await keywordStorage.addToSyncQueue({
			id: 'action1',
			type: 'create',
			data: { name: 'test' },
		});

		await keywordStorage.clearAll();

		expect(await keywordStorage.getKeywords()).toEqual([]);
		expect(await keywordStorage.getSelectedKeywords()).toEqual([]);
		expect(await keywordStorage.getSyncQueue()).toEqual([]);
	});

	test('should generate unique action IDs', () => {
		const id1 = keywordStorage.generateActionId();
		const id2 = keywordStorage.generateActionId();

		expect(id1).not.toBe(id2);
		expect(id1).toMatch(/^action_\d+_[a-z0-9]+$/);
		expect(id2).toMatch(/^action_\d+_[a-z0-9]+$/);
	});

	test('should handle IndexedDB reset', async () => {
		// This test verifies the reset functionality works
		await expect(keywordStorage.resetIndexedDB()).resolves.not.toThrow();
	});
});

console.log('Keyword system IndexedDB tests completed successfully!');
