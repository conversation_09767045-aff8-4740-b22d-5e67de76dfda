import { TranslationDict } from './translation-dict.trans';

export const errorsTranslations: TranslationDict = {
	'errors.general_fetch_error': {
		EN: 'Failed to fetch data',
		VI: 'Tải dữ liệu thất bại',
	},
	'qa_practice.errors.generation_failed_title': {
		EN: 'Generation Failed',
		VI: 'Tạo thất bại',
	},
	'errors.404_title': {
		EN: 'Oops! Page not found',
		VI: 'Oops! Trang không tìm thấy',
	},
	'errors.404_description': {
		EN: "It looks like you've gotten lost on your vocabulary learning journey. Don't worry, we'll get you back on track!",
		VI: 'C<PERSON> vẻ như bạn đã lạc đường trong hành trình học từ vựng. Đừng lo, chúng ta sẽ đưa bạn trở về đúng hướng!',
	},
	'errors.404_home_button': {
		EN: 'Go to homepage',
		VI: 'Về trang chủ',
	},
	'errors.404_collections_button': {
		EN: 'View collections',
		VI: '<PERSON><PERSON> bộ sưu tập',
	},
	'errors.404_fun_fact_prefix': {
		EN: 'Did you know?',
		VI: 'Bạn có biết?',
	},
	'errors.404_fun_fact': {
		EN: 'The term "404" comes from HTTP error codes, but in the world of vocabulary learning, it\'s just an opportunity to discover more!',
		VI: 'Từ "404" xuất phát từ mã lỗi HTTP, nhưng trong thế giới học từ vựng, đây chỉ là cơ hội để khám phá thêm!',
	},
	'errors.collections_load_error': {
		EN: 'An error occurred. You may need to log in to view collections.',
		VI: 'Có lỗi xảy ra. Bạn có thể cần đăng nhập để xem bộ sưu tập.',
	},
	// General error page translations
	'errors.general_title': {
		EN: 'Something went wrong',
		VI: 'Có lỗi xảy ra',
	},
	'errors.general_description': {
		EN: 'We encountered an unexpected error. Our team has been notified and is working to fix the issue.',
		VI: 'Chúng tôi gặp phải lỗi không mong muốn. Đội ngũ của chúng tôi đã được thông báo và đang làm việc để khắc phục sự cố.',
	},
	'errors.network_title': {
		EN: 'Connection Problem',
		VI: 'Vấn đề kết nối',
	},
	'errors.network_description': {
		EN: 'Unable to connect to our servers. Please check your internet connection and try again.',
		VI: 'Không thể kết nối đến máy chủ của chúng tôi. Vui lòng kiểm tra kết nối internet và thử lại.',
	},
	'errors.auth_title': {
		EN: 'Authentication Required',
		VI: 'Yêu cầu xác thực',
	},
	'errors.auth_description': {
		EN: 'You need to sign in to access this content.',
		VI: 'Bạn cần đăng nhập để truy cập nội dung này.',
	},
	'errors.access_denied_title': {
		EN: 'Access Denied',
		VI: 'Truy cập bị từ chối',
	},
	'errors.access_denied_description': {
		EN: "You don't have permission to access this resource.",
		VI: 'Bạn không có quyền truy cập tài nguyên này.',
	},
	'errors.validation_title': {
		EN: 'Invalid Input',
		VI: 'Dữ liệu không hợp lệ',
	},
	'errors.validation_description': {
		EN: 'Please check your input and try again.',
		VI: 'Vui lòng kiểm tra dữ liệu nhập và thử lại.',
	},
	'errors.server_title': {
		EN: 'Server Error',
		VI: 'Lỗi máy chủ',
	},
	'errors.server_description': {
		EN: 'Something went wrong on our end. Please try again later.',
		VI: 'Có lỗi xảy ra ở phía chúng tôi. Vui lòng thử lại sau.',
	},
	'errors.offline_title': {
		EN: "You're Offline",
		VI: 'Bạn đang ngoại tuyến',
	},
	'errors.offline_description': {
		EN: "It looks like you've lost your internet connection. Please check your connection and try again.",
		VI: 'Có vẻ như bạn đã mất kết nối internet. Vui lòng kiểm tra kết nối và thử lại.',
	},
	'errors.maintenance_title': {
		EN: 'Under Maintenance',
		VI: 'Đang bảo trì',
	},
	'errors.maintenance_description': {
		EN: "We're currently performing scheduled maintenance. Please check back in a few minutes.",
		VI: 'Chúng tôi đang thực hiện bảo trì theo lịch trình. Vui lòng quay lại sau vài phút.',
	},
	// Action buttons
	'errors.try_again': {
		EN: 'Try Again',
		VI: 'Thử lại',
	},
	'errors.retrying': {
		EN: 'Retrying...',
		VI: 'Đang thử lại...',
	},
	'errors.go_back': {
		EN: 'Go Back',
		VI: 'Quay lại',
	},
	'errors.go_home': {
		EN: 'Go Home',
		VI: 'Về trang chủ',
	},
	'errors.report_bug': {
		EN: 'Report Bug',
		VI: 'Báo lỗi',
	},
	'errors.refresh_page': {
		EN: 'Refresh Page',
		VI: 'Tải lại trang',
	},
	'errors.retry': {
		EN: 'Retry',
		VI: 'Thử lại',
	},
	// Error boundary specific
	'errors.boundary_page_title': {
		EN: 'Oops! Something broke',
		VI: 'Oops! Có gì đó bị hỏng',
	},
	'errors.boundary_page_description': {
		EN: "We encountered an unexpected error while loading this page. Don't worry, our team has been notified!",
		VI: 'Chúng tôi gặp phải lỗi không mong muốn khi tải trang này. Đừng lo, đội ngũ của chúng tôi đã được thông báo!',
	},
	'errors.boundary_section_title': {
		EN: 'Section Error',
		VI: 'Lỗi phần',
	},
	'errors.boundary_section_description': {
		EN: "This section encountered an error and couldn't load properly.",
		VI: 'Phần này gặp lỗi và không thể tải đúng cách.',
	},
	'errors.boundary_component_title': {
		EN: 'Component failed to load',
		VI: 'Thành phần không thể tải',
	},
	// Development
	'errors.development_details': {
		EN: 'Development Details',
		VI: 'Chi tiết phát triển',
	},
	'errors.error_id': {
		EN: 'Error ID',
		VI: 'ID lỗi',
	},
	// Fun facts for error pages
	'errors.fun_fact_prefix': {
		EN: 'Did you know?',
		VI: 'Bạn có biết?',
	},
	'errors.fun_facts': {
		EN: [
			'Learning from mistakes is one of the most effective ways to improve your vocabulary!',
			"Even the best applications encounter errors - it's how we handle them that matters.",
			'Error pages are a great opportunity to practice patience and problem-solving skills.',
			'In programming, debugging is like detective work - every error tells a story.',
			'The word "error" comes from Latin "errare" meaning "to wander" - just like learning new words!',
		],
		VI: [
			'Học từ sai lầm là một trong những cách hiệu quả nhất để cải thiện từ vựng của bạn!',
			'Ngay cả những ứng dụng tốt nhất cũng gặp lỗi - điều quan trọng là cách chúng ta xử lý chúng.',
			'Trang lỗi là cơ hội tuyệt vời để luyện tập sự kiên nhẫn và kỹ năng giải quyết vấn đề.',
			'Trong lập trình, debug giống như công việc thám tử - mỗi lỗi đều kể một câu chuyện.',
			'Từ "error" xuất phát từ tiếng Latin "errare" có nghĩa là "đi lang thang" - giống như học từ mới!',
		],
	},
};
