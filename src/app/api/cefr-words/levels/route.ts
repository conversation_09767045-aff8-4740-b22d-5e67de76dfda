import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling, withValidation } from '@/lib/api-error-middleware';
import { CefrLevel } from '@/models/cefr-word';

// GET /api/cefr-words/levels - Get all CEFR levels and their statistics
export const GET = withErrorHandling(
	async (request: NextRequest) => {
		const cefrWordService = getCefrWordService();
		
		const [levels, levelStats, levelDistribution] = await Promise.all([
			cefrWordService.getAllCefrLevels(),
			cefrWordService.getCefrLevelStats(),
			cefrWordService.getLevelDistribution(),
		]);
		
		return NextResponse.json({
			success: true,
			data: {
				levels,
				statistics: levelStats,
				distribution: levelDistribution,
			},
		});
	}
);
