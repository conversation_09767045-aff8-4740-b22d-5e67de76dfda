import { NextRequest, NextResponse } from 'next/server';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';

// GET /api/cefr-words/stats - Get comprehensive CEFR words statistics
export const GET = withErrorHandling(
	async (request: NextRequest) => {
		const cefrWordService = getCefrWordService();
		
		const [
			totalCount,
			wordCountByLevel,
			levelDistribution,
			levelStats
		] = await Promise.all([
			cefrWordService.getTotalWordCount(),
			cefrWordService.getWordCountByLevel(),
			cefrWordService.getLevelDistribution(),
			cefrWordService.getCefrLevelStats(),
		]);
		
		return NextResponse.json({
			success: true,
			data: {
				total_words: totalCount,
				by_level: wordCountByLevel,
				distribution: levelDistribution,
				level_statistics: levelStats,
				summary: {
					most_populated_level: wordCountByLevel.reduce((max, current) => 
						current.count > max.count ? current : max, 
						{ level: '', count: 0 }
					),
					least_populated_level: wordCountByLevel.reduce((min, current) => 
						current.count < min.count ? current : min, 
						{ level: '', count: Infinity }
					),
					average_words_per_level: Math.round(totalCount / wordCountByLevel.length),
				},
			},
		});
	}
);
