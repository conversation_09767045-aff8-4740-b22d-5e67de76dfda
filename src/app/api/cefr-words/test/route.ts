import { NextRequest, NextResponse } from 'next/server';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { CefrLevel } from '@/models/cefr-word';

// GET /api/cefr-words/test - Test CEFR words functionality
export const GET = withErrorHandling(
	async (request: NextRequest) => {
		const cefrWordService = getCefrWordService();
		
		try {
			// Test 1: Create a test word
			const testWord = await cefrWordService.createCefrWord({
				term: 'test-word-' + Date.now(),
				level: CefrLevel.A1,
			});
			
			// Test 2: Get word by ID
			const retrievedWord = await cefrWordService.getCefrWordById(testWord.id);
			
			// Test 3: Search words
			const searchResults = await cefrWordService.searchCefrWords({
				term: 'test',
				limit: 5,
			});
			
			// Test 4: Get words by level
			const a1Words = await cefrWordService.getCefrWordsByLevel(CefrLevel.A1, 5);
			
			// Test 5: Get level stats
			const levelStats = await cefrWordService.getCefrLevelStats();
			
			// Test 6: Check word level
			const wordLevel = await cefrWordService.checkWordLevel(testWord.term);
			
			// Test 7: Get recommendations
			const recommendations = await cefrWordService.getVocabularyRecommendations(
				CefrLevel.A1,
				[],
				5
			);
			
			// Clean up: Delete test word
			await cefrWordService.deleteCefrWord(testWord.id);
			
			return NextResponse.json({
				success: true,
				message: 'All CEFR words tests passed successfully!',
				test_results: {
					create_word: !!testWord,
					retrieve_word: !!retrievedWord && retrievedWord.id === testWord.id,
					search_words: Array.isArray(searchResults),
					get_words_by_level: Array.isArray(a1Words),
					get_level_stats: Array.isArray(levelStats),
					check_word_level: wordLevel === CefrLevel.A1,
					get_recommendations: !!recommendations && !!recommendations.current_level,
					cleanup: true,
				},
				data: {
					created_word: testWord,
					search_count: searchResults.length,
					a1_words_count: a1Words.length,
					level_stats_count: levelStats.length,
					recommendations_count: recommendations.recommended_words.length,
				},
			});
		} catch (error) {
			return NextResponse.json({
				success: false,
				error: 'CEFR words test failed',
				details: error instanceof Error ? error.message : 'Unknown error',
			}, { status: 500 });
		}
	}
);
