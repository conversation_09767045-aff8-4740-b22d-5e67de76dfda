import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling, withValidation, withAuth } from '@/lib/api-error-middleware';
import { CefrLevel } from '@/models/cefr-word';

// GET /api/cefr-words/recommendations - Get vocabulary recommendations based on current level
const GetRecommendationsSchema = z.object({
	currentLevel: z.nativeEnum(CefrLevel),
	knownWords: z.array(z.string()).optional().default([]),
	limit: z.coerce.number().min(1).max(50).default(20),
});

export const GET = withErrorHandling(
	withValidation(GetRecommendationsSchema, 'query')(
		async (request: NextRequest, { validatedData }: { validatedData: z.infer<typeof GetRecommendationsSchema> }) => {
			const cefrWordService = getCefrWordService();
			
			const recommendations = await cefrWordService.getVocabularyRecommendations(
				validatedData.currentLevel,
				validatedData.knownWords,
				validatedData.limit
			);
			
			return NextResponse.json({
				success: true,
				data: recommendations,
			});
		}
	)
);

// POST /api/cefr-words/recommendations/check-level - Check the CEFR level of a word
const CheckLevelSchema = z.object({
	term: z.string().min(1, 'Term is required'),
});

export const POST = withErrorHandling(
	withValidation(CheckLevelSchema, 'body')(
		async (request: NextRequest, { validatedData }: { validatedData: z.infer<typeof CheckLevelSchema> }) => {
			const cefrWordService = getCefrWordService();
			
			const level = await cefrWordService.checkWordLevel(validatedData.term);
			
			return NextResponse.json({
				success: true,
				data: {
					term: validatedData.term,
					level,
					found: level !== null,
				},
			});
		}
	)
);
