# CEFR Words API Documentation

This API provides comprehensive management and access to CEFR (Common European Framework of Reference for Languages) vocabulary words.

## Base URL
```
/api/cefr-words
```

## Authentication
Most endpoints require authentication. Admin-only endpoints are marked with 🔒.

## Endpoints

### 1. Get/Search CEFR Words
```http
GET /api/cefr-words
```

**Query Parameters:**
- `term` (optional): Search term to filter words
- `level` (optional): CEFR level (A1, A2, B1, B2, C1, C2)
- `levels` (optional): Array of CEFR levels
- `limit` (optional): Number of results (1-100, default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Example:**
```http
GET /api/cefr-words?term=hello&level=A1&limit=10
```

### 2. Create CEFR Word 🔒
```http
POST /api/cefr-words
```

**Body:**
```json
{
  "term": "hello",
  "level": "A1"
}
```

### 3. Bulk Create CEFR Words 🔒
```http
PUT /api/cefr-words
```

**Body:**
```json
{
  "words": [
    {"term": "hello", "level": "A1"},
    {"term": "goodbye", "level": "A1"}
  ],
  "skipDuplicates": true
}
```

### 4. Get CEFR Word by ID
```http
GET /api/cefr-words/{id}
```

### 5. Update CEFR Word 🔒
```http
PUT /api/cefr-words/{id}
```

**Body:**
```json
{
  "term": "updated-term",
  "level": "A2"
}
```

### 6. Delete CEFR Word 🔒
```http
DELETE /api/cefr-words/{id}
```

### 7. Get All Levels and Statistics
```http
GET /api/cefr-words/levels
```

**Response:**
```json
{
  "success": true,
  "data": {
    "levels": ["A1", "A2", "B1", "B2", "C1", "C2"],
    "statistics": [
      {"level": "A1", "total_words": 1500},
      {"level": "A2", "total_words": 2000}
    ],
    "distribution": {
      "A1": 1500,
      "A2": 2000,
      "B1": 2500,
      "B2": 3000,
      "C1": 3500,
      "C2": 4000
    }
  }
}
```

### 8. Get Words by Level
```http
GET /api/cefr-words/levels/{level}
```

**Query Parameters:**
- `limit` (optional): Number of results (1-100, default: 50)
- `random` (optional): Get random words (default: false)

**Example:**
```http
GET /api/cefr-words/levels/A1?limit=20&random=true
```

### 9. Search Words
```http
GET /api/cefr-words/search
```

**Query Parameters:**
- `q` (required): Search query
- `level` (optional): Filter by CEFR level
- `limit` (optional): Number of results (1-100, default: 20)

**Example:**
```http
GET /api/cefr-words/search?q=hello&level=A1
```

### 10. Batch Search
```http
POST /api/cefr-words/search/batch
```

**Body:**
```json
{
  "terms": ["hello", "world", "goodbye"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "terms": ["hello", "world", "goodbye"],
    "results": [
      {"term": "hello", "found": true, "word": {...}},
      {"term": "world", "found": false, "word": null},
      {"term": "goodbye", "found": true, "word": {...}}
    ],
    "found_count": 2,
    "total_searched": 3
  }
}
```

### 11. Get Vocabulary Recommendations
```http
GET /api/cefr-words/recommendations
```

**Query Parameters:**
- `currentLevel` (required): Current CEFR level
- `knownWords` (optional): Array of known words to exclude
- `limit` (optional): Number of recommendations (1-50, default: 20)

**Example:**
```http
GET /api/cefr-words/recommendations?currentLevel=A1&limit=10
```

### 12. Check Word Level
```http
POST /api/cefr-words/recommendations/check-level
```

**Body:**
```json
{
  "term": "hello"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "term": "hello",
    "level": "A1",
    "found": true
  }
}
```

### 13. Get Statistics
```http
GET /api/cefr-words/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_words": 16500,
    "by_level": [...],
    "distribution": {...},
    "level_statistics": [...],
    "summary": {
      "most_populated_level": {"level": "C2", "count": 4000},
      "least_populated_level": {"level": "A1", "count": 1500},
      "average_words_per_level": 2750
    }
  }
}
```

### 14. Test Endpoint
```http
GET /api/cefr-words/test
```

Runs comprehensive tests on all CEFR words functionality.

## CEFR Levels

The API supports the following CEFR levels:
- **A1**: Beginner
- **A2**: Elementary
- **B1**: Intermediate
- **B2**: Upper Intermediate
- **C1**: Advanced
- **C2**: Proficient

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": "Additional error details"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `404`: Not Found
- `500`: Internal Server Error

## Rate Limiting

API endpoints are subject to rate limiting. Please refer to the main API documentation for current limits.

## Examples

### Create and manage CEFR words
```javascript
// Create a new word
const response = await fetch('/api/cefr-words', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ term: 'hello', level: 'A1' })
});

// Search words
const searchResponse = await fetch('/api/cefr-words/search?q=hello');
const { data } = await searchResponse.json();

// Get recommendations
const recResponse = await fetch('/api/cefr-words/recommendations?currentLevel=A1');
const recommendations = await recResponse.json();
```
