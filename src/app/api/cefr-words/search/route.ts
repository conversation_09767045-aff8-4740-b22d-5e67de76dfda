import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling, withValidation } from '@/lib/api-error-middleware';
import { CefrLevel } from '@/models/cefr-word';

// GET /api/cefr-words/search - Search CEFR words by term
const SearchCefrWordsSchema = z.object({
	q: z.string().min(1, 'Search query is required'),
	level: z.nativeEnum(CefrLevel).optional(),
	limit: z.coerce.number().min(1).max(100).default(20),
});

export const GET = withErrorHandling(
	withValidation(SearchCefrWordsSchema, 'query')(
		async (request: NextRequest, { validatedData }: { validatedData: z.infer<typeof SearchCefrWordsSchema> }) => {
			const cefrWordService = getCefrWordService();
			
			const searchOptions = {
				term: validatedData.q,
				level: validatedData.level,
				limit: validatedData.limit,
			};

			const words = await cefrWordService.searchCefrWords(searchOptions);
			
			return NextResponse.json({
				success: true,
				data: {
					query: validatedData.q,
					level: validatedData.level,
					words,
					count: words.length,
				},
			});
		}
	)
);

// POST /api/cefr-words/search/batch - Search multiple terms at once
const BatchSearchSchema = z.object({
	terms: z.array(z.string().min(1)).min(1, 'At least one term is required').max(50, 'Maximum 50 terms allowed'),
});

export const POST = withErrorHandling(
	withValidation(BatchSearchSchema, 'body')(
		async (request: NextRequest, { validatedData }: { validatedData: z.infer<typeof BatchSearchSchema> }) => {
			const cefrWordService = getCefrWordService();
			
			const words = await cefrWordService.getCefrWordsByTerms(validatedData.terms);
			
			// Create a map for quick lookup
			const wordMap = new Map(words.map(word => [word.term.toLowerCase(), word]));
			
			// Create results array maintaining the order of input terms
			const results = validatedData.terms.map(term => {
				const word = wordMap.get(term.toLowerCase());
				return {
					term,
					found: !!word,
					word: word || null,
				};
			});
			
			return NextResponse.json({
				success: true,
				data: {
					terms: validatedData.terms,
					results,
					found_count: results.filter(r => r.found).length,
					total_searched: validatedData.terms.length,
				},
			});
		}
	)
);
