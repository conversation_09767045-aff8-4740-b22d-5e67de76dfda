import { getSeoService } from '@/backend/wire';
import { withErrorHandling, withValidation } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { clearSeoCache } from '@/lib/seo';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for SEO settings
const seoSettingsSchema = z.object({
	title: z
		.string()
		.min(1, 'Title is required')
		.max(60, 'Title should be 60 characters or less')
		.optional(),
	description: z
		.string()
		.min(1, 'Description is required')
		.max(160, 'Description should be 160 characters or less')
		.optional(),
	keywords: z.string().max(255, 'Keywords should be 255 characters or less').optional(),

	og_title: z.string().max(60, 'OG title should be 60 characters or less').optional().nullable(),
	og_description: z
		.string()
		.max(160, 'OG description should be 160 characters or less')
		.optional()
		.nullable(),
	og_image_url: z
		.string()
		.url('OG image URL must be valid')
		.or(z.string().regex(/^\//, 'OG image URL must be a valid path'))
		.optional()
		.nullable(),
	og_type: z.string().optional(),
	twitter_card: z.enum(['summary', 'summary_large_image', 'app', 'player']).optional(),
	twitter_site: z.string().optional().nullable(),
	twitter_creator: z.string().optional().nullable(),
	canonical_url: z.string().url('Canonical URL must be valid').optional().nullable(),
	robots: z.string().optional(),
	language: z
		.string()
		.regex(/^[a-z]{2}(-[A-Z]{2})?$/, 'Language must be in ISO 639-1 format')
		.optional(),
	theme_color: z
		.string()
		.regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Theme color must be a valid hex color')
		.optional(),
	background_color: z
		.string()
		.regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Background color must be a valid hex color')
		.optional(),
});

/**
 * GET /api/admin/seo
 * Get current SEO settings
 */
async function handleGet(request: NextRequest) {
	try {
		const seoService = getSeoService();
		const settings = await seoService.getOrCreateDefaultSeoSettings();

		return NextResponse.json(settings);
	} catch (error) {
		console.error('Failed to get SEO settings:', error);
		throw new Error('Failed to get SEO settings');
	}
}

/**
 * PUT /api/admin/seo
 * Update SEO settings
 */
async function handlePut(
	request: NextRequest,
	context: { validatedData: z.infer<typeof seoSettingsSchema>; [key: string]: any }
) {
	try {
		const seoService = getSeoService();

		// Update SEO settings (data already validated by Zod)
		const updatedSettings = await seoService.updateSeoSettings(context.validatedData);

		// Clear SEO cache to ensure fresh data on next request
		clearSeoCache();

		return NextResponse.json(updatedSettings);
	} catch (error) {
		console.error('Failed to update SEO settings:', error);
		throw new Error('Failed to update SEO settings');
	}
}

// Apply middleware and export handlers
export const GET = withAdminAuth(withErrorHandling(handleGet));

export const PUT = withAdminAuth(withValidation(seoSettingsSchema, 'body')(handlePut));

// For reset endpoint, we'll handle it in a separate route file
// since Next.js doesn't support custom methods in route handlers
