import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWordService } from '@/backend/wire';
import { withAuth, withErrorHandling, withValidation } from '@/lib/api-error-middleware';
import { Language, Role } from '@prisma/client';

const CleanupWordsSchema = z.object({
	language: z.nativeEnum(Language).optional(),
	batchSize: z.number().min(1).max(1000).default(100),
	dryRun: z.boolean().default(true),
});

type CleanupWordsRequest = z.infer<typeof CleanupWordsSchema>;

/**
 * POST /api/admin/cleanup-words
 *
 * Admin endpoint to cleanup words without definitions
 * Requires ADMIN role
 */
async function cleanupWordsHandler(
	request: NextRequest,
	context: { validatedData: CleanupWordsRequest; [key: string]: any }
) {
	// Note: Admin authentication is handled by withAuth middleware
	// This handler assumes the user is already authenticated and authorized

	const { language, batchSize, dryRun } = context.validatedData;
	const wordService = getWordService();

	try {
		if (dryRun) {
			// For dry run, just find and count words without definitions
			const wordsWithoutDefinitions = await wordService.findWordsWithoutDefinitions(
				language,
				1000 // Limit for preview
			);

			return NextResponse.json({
				success: true,
				dryRun: true,
				preview: {
					totalFound: wordsWithoutDefinitions.length,
					sampleWords: wordsWithoutDefinitions.slice(0, 10).map((word) => ({
						id: word.id,
						term: word.term,
						language: word.language,
						createdAt: word.created_at,
					})),
					wouldDelete: wordsWithoutDefinitions.length,
					batchSize,
				},
			});
		} else {
			// Actual cleanup
			const result = await wordService.deleteWordsWithoutDefinitions(language, batchSize);

			return NextResponse.json({
				success: true,
				dryRun: false,
				result: {
					deletedCount: result.deletedCount,
					affectedCollections: result.affectedCollections,
					language: language || 'ALL',
					batchSize,
				},
			});
		}
	} catch (error) {
		console.error('Cleanup words error:', error);
		return NextResponse.json(
			{
				error: 'Failed to cleanup words',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

export const POST = withErrorHandling(
	withAuth(withValidation(CleanupWordsSchema, 'body')(cleanupWordsHandler))
);

/**
 * GET /api/admin/cleanup-words
 *
 * Get statistics about words without definitions
 */
async function getCleanupStatsHandler(request: NextRequest, { user }: { user: any }) {
	// Check if user is admin
	if (user.role !== Role.ADMIN) {
		return NextResponse.json(
			{ error: 'Insufficient permissions. Admin role required.' },
			{ status: 403 }
		);
	}

	const wordService = getWordService();

	try {
		// Get counts for both languages
		const [englishWords, vietnameseWords, allWords] = await Promise.all([
			wordService.findWordsWithoutDefinitions(Language.EN, 1),
			wordService.findWordsWithoutDefinitions(Language.VI, 1),
			wordService.findWordsWithoutDefinitions(undefined, 1),
		]);

		// Get actual counts (we only fetched 1 to check if any exist)
		const [englishCount, vietnameseCount, totalCount] = await Promise.all([
			wordService.findWordsWithoutDefinitions(Language.EN).then((words) => words.length),
			wordService.findWordsWithoutDefinitions(Language.VI).then((words) => words.length),
			wordService.findWordsWithoutDefinitions().then((words) => words.length),
		]);

		return NextResponse.json({
			success: true,
			stats: {
				total: totalCount,
				byLanguage: {
					EN: englishCount,
					VI: vietnameseCount,
				},
				hasWordsToCleanup: totalCount > 0,
			},
		});
	} catch (error) {
		console.error('Get cleanup stats error:', error);
		return NextResponse.json(
			{
				error: 'Failed to get cleanup statistics',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

export const GET = withErrorHandling(withAuth(getCleanupStatsHandler));
