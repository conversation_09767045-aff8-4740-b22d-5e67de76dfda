import { UnauthorizedError } from '@/backend/errors';
import { getUserService } from '@/backend/wire';
import { auth } from '@/lib';
import { createErrorContext, errorLogger } from '@/lib/error-handling';
import { NextResponse } from 'next/server';

export async function GET() {
	try {
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError('Unauthorized');
		}

		const userService = getUserService();
		const user = await userService.getUserById(userId);

		if (!user) {
			throw new UnauthorizedError('User not found');
		}

		return NextResponse.json(user);
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		// Enhanced error logging
		errorLogger.error(
			'Failed to get current user',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('UserAPI', 'get_current_user'),
			'UserAPI'
		);

		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
