// ============================================================================
// TYPES
// ============================================================================

export interface DatabaseConfig {
	url: string;
	connectionLimit: number;
	connectTimeout: number;
	queryTimeout: number;
	poolTimeout: number;
	logLevel: ('query' | 'info' | 'warn' | 'error')[];
}

// ============================================================================
// CONFIGURATION FUNCTIONS
// ============================================================================

/**
 * Get database configuration
 * Contains connection pooling and timeout settings optimized for the environment
 */
export function getDatabaseConfig(): DatabaseConfig {
	const env = process.env;
	const isDevelopment = env.NODE_ENV === 'development';
	const isProduction = env.NODE_ENV === 'production';

	return {
		url:
			env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/vocab?schema=public',
		// Connection pool limits - lower for development to prevent exhaustion
		connectionLimit: isDevelopment ? 5 : isProduction ? 20 : 10,
		// Connection timeout (10 seconds)
		connectTimeout: Number.parseInt(env.DB_CONNECT_TIMEOUT || '10000', 10),
		// Query timeout (30 seconds for complex queries)
		queryTimeout: Number.parseInt(env.DB_QUERY_TIMEOUT || '30000', 10),
		// Pool timeout (10 seconds)
		poolTimeout: Number.parseInt(env.DB_POOL_TIMEOUT || '10000', 10),
		// Logging configuration
		logLevel: isDevelopment ? ['error', 'warn'] : isProduction ? ['error'] : ['error', 'warn'],
	};
}

/**
 * Parse connection limit from DATABASE_URL if specified
 * PostgreSQL URL format: postgresql://user:pass@host:port/db?connection_limit=10
 */
export function parseConnectionLimitFromUrl(url: string): number | undefined {
	try {
		const urlObj = new URL(url);
		const connectionLimit = urlObj.searchParams.get('connection_limit');
		return connectionLimit ? Number.parseInt(connectionLimit, 10) : undefined;
	} catch {
		return undefined;
	}
}

/**
 * Build optimized DATABASE_URL with connection parameters
 */
export function buildOptimizedDatabaseUrl(baseUrl: string, config: DatabaseConfig): string {
	try {
		const url = new URL(baseUrl);

		// Add connection pooling parameters
		url.searchParams.set('connection_limit', config.connectionLimit.toString());
		url.searchParams.set('pool_timeout', Math.floor(config.poolTimeout / 1000).toString());

		// Add connection timeout if not already present
		if (!url.searchParams.has('connect_timeout')) {
			url.searchParams.set(
				'connect_timeout',
				Math.floor(config.connectTimeout / 1000).toString()
			);
		}

		// Ensure schema is set
		if (!url.searchParams.has('schema')) {
			url.searchParams.set('schema', 'public');
		}

		return url.toString();
	} catch {
		// If URL parsing fails, return original URL
		return baseUrl;
	}
}
