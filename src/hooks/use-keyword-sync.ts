import { useCallback, useEffect, useRef, useState } from 'react';
import { keywordStorage, KeywordSyncAction } from '@/lib/keyword-storage';
import { KeywordWithDetail } from '@/models';

interface UseKeywordSyncOptions {
	onSyncSuccess?: (action: KeywordSyncAction) => void;
	onSyncError?: (action: KeywordSyncAction, error: Error) => void;
	syncInterval?: number; // milliseconds
	hideSuccessDelay?: number; // milliseconds to hide success indicator
}

/**
 * Hook for managing background synchronization of keywords
 */
export function useKeywordSync(options: UseKeywordSyncOptions = {}) {
	const {
		onSyncSuccess,
		onSyncError,
		syncInterval = 5000, // 5 seconds default
		hideSuccessDelay = 2000, // 2 seconds default
	} = options;

	const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const isSyncingRef = useRef(false);
	const hideSuccessTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const [showSyncSuccess, setShowSyncSuccess] = useState(false);

	/**
	 * Sync a single action with the server
	 */
	const syncAction = useCallback(
		async (action: KeywordSyncAction): Promise<boolean> => {
			try {
				let response: Response;

				switch (action.type) {
					case 'create':
						if (!action.data?.name) {
							throw new Error('Missing name for create action');
						}
						response = await fetch('/api/keywords', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ name: action.data.name }),
						});
						break;

					case 'update':
						if (!action.data?.keywordId || !action.data?.name) {
							throw new Error('Missing keywordId or name for update action');
						}
						response = await fetch(`/api/keywords/${action.data.keywordId}`, {
							method: 'PUT',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ name: action.data.name }),
						});
						break;

					case 'delete':
						if (!action.data?.keywordId) {
							throw new Error('Missing keywordId for delete action');
						}
						response = await fetch(`/api/keywords/${action.data.keywordId}`, {
							method: 'DELETE',
						});

						// If the keyword doesn't exist on the server (404), consider it successfully deleted
						if (response.status === 404) {
							console.warn(
								`Keyword ${action.data.keywordId} not found on server, considering delete successful`
							);
							return true;
						}
						break;

					default:
						throw new Error(`Unknown action type: ${action.type}`);
				}

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || `Failed to ${action.type} keyword`);
				}

				// For create actions, replace temporary keyword with server response
				if (action.type === 'create') {
					const serverKeyword = await response.json();
					console.log(`Keyword created on server:`, serverKeyword);

					// Find and replace the temporary keyword with server data
					const localKeywords = await keywordStorage.getKeywords();
					const tempKeyword = localKeywords.find(
						(k) => k.content === action.data?.name && k.id.startsWith('temp_')
					);

					if (tempKeyword) {
						// Remove the temporary keyword
						await keywordStorage.deleteKeywordLocally(tempKeyword.id);

						// Add the server keyword
						await keywordStorage.addKeywordLocally(serverKeyword);

						// Update selected keywords if the temp keyword was selected
						const selectedKeywords = await keywordStorage.getSelectedKeywords();
						if (selectedKeywords.includes(tempKeyword.id)) {
							const updatedSelected = selectedKeywords.map((id) =>
								id === tempKeyword.id ? serverKeyword.id : id
							);
							await keywordStorage.saveSelectedKeywords(updatedSelected);
						}

						// Clean up any related sync actions
						await keywordStorage.cleanupTemporaryKeywordActions(
							tempKeyword.id,
							serverKeyword.id
						);
					}
				}

				onSyncSuccess?.(action);

				// Show success indicator briefly
				setShowSyncSuccess(true);
				if (hideSuccessTimeoutRef.current) {
					clearTimeout(hideSuccessTimeoutRef.current);
				}
				hideSuccessTimeoutRef.current = setTimeout(() => {
					setShowSyncSuccess(false);
				}, hideSuccessDelay);

				return true;
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Unknown sync error');
				onSyncError?.(action, err);
				return false;
			}
		},
		[onSyncSuccess, onSyncError, hideSuccessDelay]
	);

	/**
	 * Process the sync queue
	 */
	const processSyncQueue = useCallback(async () => {
		if (isSyncingRef.current) return;

		isSyncingRef.current = true;
		const queue = await keywordStorage.getSyncQueue();

		for (const action of queue) {
			try {
				const success = await syncAction(action);
				if (success) {
					await keywordStorage.removeFromSyncQueue(action.id);
				} else {
					// If sync fails, we keep the action in queue for retry
					// You might want to implement exponential backoff here
					break;
				}
			} catch (error) {
				console.error(`Failed to process sync action ${action.id}:`, error);

				// For critical errors, remove the action to prevent infinite loops
				if (error instanceof Error && error.message.includes('Missing')) {
					console.warn(`Removing invalid sync action ${action.id}: ${error.message}`);
					await keywordStorage.removeFromSyncQueue(action.id);
				} else {
					// For other errors, keep the action for retry but break the loop
					break;
				}
			}
		}

		isSyncingRef.current = false;
	}, [syncAction]);

	/**
	 * Start background sync
	 */
	const startSync = useCallback(() => {
		if (syncIntervalRef.current) return;

		// Initial sync
		processSyncQueue();

		// Set up interval
		syncIntervalRef.current = setInterval(() => {
			processSyncQueue();
		}, syncInterval);
	}, [processSyncQueue, syncInterval]);

	/**
	 * Stop background sync
	 */
	const stopSync = useCallback(() => {
		if (syncIntervalRef.current) {
			clearInterval(syncIntervalRef.current);
			syncIntervalRef.current = null;
		}
	}, []);

	/**
	 * Force sync now
	 */
	const syncNow = useCallback(async () => {
		await processSyncQueue();
	}, [processSyncQueue]);

	/**
	 * Get sync queue status
	 */
	const getSyncStatus = useCallback(async () => {
		const queue = await keywordStorage.getSyncQueue();
		return {
			pendingActions: queue.length,
			isSync: isSyncingRef.current,
			hasUnsyncedChanges: queue.length > 0,
			showSyncSuccess,
		};
	}, [showSyncSuccess]);

	// Auto-start sync on mount and cleanup on unmount
	useEffect(() => {
		startSync();
		return () => {
			stopSync();
			if (hideSuccessTimeoutRef.current) {
				clearTimeout(hideSuccessTimeoutRef.current);
			}
		};
	}, [startSync, stopSync]);

	// Handle visibility change to sync when tab becomes visible
	useEffect(() => {
		const handleVisibilityChange = () => {
			if (!document.hidden) {
				syncNow();
			}
		};

		document.addEventListener('visibilitychange', handleVisibilityChange);
		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
		};
	}, [syncNow]);

	/**
	 * Reset IndexedDB in case of database errors
	 */
	const resetIndexedDBStorage = useCallback(async (): Promise<void> => {
		try {
			await keywordStorage.resetIndexedDB();
			// Restart sync after reset
			stopSync();
			startSync();
			return Promise.resolve();
		} catch (error) {
			console.error('Failed to reset IndexedDB storage:', error);
			return Promise.reject(error);
		}
	}, [startSync, stopSync]);

	return {
		startSync,
		stopSync,
		syncNow,
		getSyncStatus,
		resetIndexedDBStorage,
		isSync: isSyncingRef.current,
	};
}
