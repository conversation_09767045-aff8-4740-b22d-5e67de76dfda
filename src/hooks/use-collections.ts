'use client';

// Removed direct backend API imports - now using HTTP requests
import { CollectionsContext, CollectionsContextType, useLoadingError } from '@/contexts';
import { useErrorHandler } from '@/contexts/error-context';
import { createErrorContext, normalizeError, retryApiCall } from '@/lib/error-management';
import { getAllCollections, saveCollection, saveCollections } from '@/lib/indexed-db';
import { CollectionWithDetail, WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { useCallback, useContext, useEffect } from 'react';

/*
 * Note: Many useCallback hooks in this file intentionally exclude context functions
 * like loading<PERSON><PERSON>r<PERSON><PERSON><PERSON>, setError, handleError from dependency arrays.
 * These functions are stable from React context and including them would cause
 * unnecessary re-renders or infinite loops.
 *
 * ESLint exhaustive-deps rule is disabled for this file due to extensive use
 * of stable context functions that don't need to be included in dependency arrays.
 */

export function useCollectionsContext(): CollectionsContextType {
	const context = useContext(CollectionsContext);
	if (context === undefined)
		throw new Error('useCollectionsContext must be used within a CollectionsProvider');
	return context;
}

export function useCollections(initialCollections?: CollectionWithDetail[]) {
	const {
		loading,
		error,
		setError,
		setCollections,

		collections,
		currentCollection,
		setCurrentCollection,

		currentCollectionWords,
		setCurrentCollectionWords,
	} = useCollectionsContext();
	const loadingErrorHelper = useLoadingError('collections');
	const { handleError } = useErrorHandler('useCollections');

	useEffect(() => {
		if (initialCollections) {
			setCollections(initialCollections);
		}
	}, [initialCollections, setCollections]);

	const fetchCollections = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetch');
		start();
		try {
			const collectionsData = await retryApiCall(async () => {
				const response = await fetch('/api/collections');
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to fetch collections');
				}
				return response.json();
			});
			// Save to cache after getting data from API
			setCollections(collectionsData as CollectionWithDetail[]);
			await saveCollections(collectionsData as CollectionWithDetail[]);
			end();
		} catch (apiError) {
			const error = normalizeError(
				apiError,
				'Failed to fetch collections',
				createErrorContext('useCollections', 'fetch_collections')
			);
			handleError(error, 'fetch_collections');
			end(error);
		}
		// Context functions are stable, disabling exhaustive-deps
	}, []);

	const getCollection = useCallback(async (id: string) => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'get');
		start();
		try {
			const response = await fetch(`/api/collections/${id}`);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to fetch collection');
			}
			const collectionData = await response.json();
			await saveCollection(collectionData);
			end();
			return collectionData;
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to fetch collection'));
			return null;
		}
		// Context functions are stable, disabling exhaustive-deps
	}, []);

	const getCollectionWordsToReview = useCallback(async (id: string, limit?: number) => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'getWordsToReview');
		start();
		try {
			const url = new URL(`/api/collections/${id}/vocabulary/review`, window.location.origin);
			if (limit) url.searchParams.set('limit', limit.toString());

			const response = await fetch(url.toString());
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to get review words');
			}
			const wordsFromApi = await response.json();
			end();
			return wordsFromApi;
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to get review words'));
			return [];
		}
		// Context functions are stable, disabling exhaustive-deps
	}, []);

	const searchCollections = useCallback(
		async (term: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'search');
			start();
			try {
				// Search in current collections first
				if (collections.length > 0) {
					const filtered = collections.filter((collection) =>
						collection.name.toLowerCase().includes(term.toLowerCase())
					);
					end();
					return filtered;
				}

				// If no collections loaded, fetch and search
				const response = await fetch('/api/collections');
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to fetch collections');
				}
				const result = await response.json();
				const filtered = (result as CollectionWithDetail[]).filter((collection) =>
					collection.name.toLowerCase().includes(term.toLowerCase())
				);
				end();
				return filtered;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to search'));
				return [];
			}
		},
		[collections]
	);

	const createCollection = useCallback(
		async (name: string, target_language: Language, source_language: Language) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'create');
			start();
			try {
				const response = await fetch('/api/collections', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						name,
						target_language,
						source_language,
					}),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to create collection');
				}

				const newCollection = await response.json();
				await saveCollection(newCollection); // Save to IndexedDB
				setCollections((prevCollections) => [...prevCollections, newCollection]); // Add to state
				end();
				return newCollection;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to create collection'));
				return null;
			}
		},
		[]
	);

	const updateCollection = useCallback(
		async (id: string, data: { name?: string }) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'update');
			start();
			try {
				const response = await fetch(`/api/collections/${id}`, {
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(data),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to update collection');
				}

				const updatedCollection = await response.json();
				await saveCollection(updatedCollection);
				setCollections((prevCollections) =>
					prevCollections.map((c) => (c.id === id ? { ...c, ...updatedCollection } : c))
				);
				if (currentCollection?.id === id) setCurrentCollection(updatedCollection);
				end();
				return updatedCollection;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to update collection'));
				return null;
			}
		},
		[currentCollection]
	);

	const deleteCollection = useCallback(
		async (id: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'delete');
			start();
			try {
				const response = await fetch(`/api/collections/${id}`, {
					method: 'DELETE',
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to delete collection');
				}

				setCollections((prev) => prev.filter((c) => c.id !== id));
				if (currentCollection?.id === id) setCurrentCollection(null);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to delete'));
			}
		},
		[currentCollection]
	);

	const refreshCollection = useCallback(
		async (id: string): Promise<CollectionWithDetail | null> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'get');
			start();
			try {
				const response = await fetch(`/api/collections/${id}`);
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to refresh collection');
				}
				const collectionData = (await response.json()) as CollectionWithDetail;
				await saveCollection(collectionData);
				setCollections((prev) => prev.map((c) => (c.id === id ? collectionData : c)));
				if (currentCollection?.id === id) setCurrentCollection(collectionData);
				end();
				return collectionData;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to refresh collection'));
				return null;
			}
		},
		[currentCollection]
	);

	const addTermToCollection = useCallback(
		async (collectionId: string, term: string, language: Language) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'addTerm');
			start();
			try {
				const response = await fetch(`/api/collections/${collectionId}/words`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ term, language }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to add term to collection');
				}

				const result = await response.json();
				// Update local state and cache
				await saveCollection(result);
				setCollections((prev) => prev.map((c) => (c.id === collectionId ? result : c)));
				// Update current collection if it's the same
				if (currentCollection?.id === collectionId) {
					setCurrentCollection(result);
				}
				end();
				return result;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to add term'));
				return null;
			}
		},
		[currentCollection]
	);

	const addWordsToCollection = useCallback(
		async (collectionId: string, wordIds: string[]) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'addWords');
			start();
			try {
				const response = await fetch(`/api/collections/${collectionId}/words`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ wordIds }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to add words to collection');
				}

				const result = await response.json();
				// Update local state and cache
				await saveCollection(result);
				setCollections((prev) => prev.map((c) => (c.id === collectionId ? result : c)));
				// Update current collection if it's the same
				if (currentCollection?.id === collectionId) setCurrentCollection(result);
				end();
				return result;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to add words'));
				return null;
			}
		},
		[currentCollection]
	);

	const removeWordsFromCollection = useCallback(
		async (collectionId: string, wordIds: string[]) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'removeWords');
			start();
			try {
				const response = await fetch(`/api/collections/${collectionId}/words`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ wordIds }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to remove words from collection');
				}

				const result = await response.json();
				// Update local state and cache
				await saveCollection(result);
				setCollections((prev) => prev.map((c) => (c.id === collectionId ? result : c)));
				// Update current collection if it's the same
				if (currentCollection?.id === collectionId) setCurrentCollection(result);
				end();
				return result;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to remove words'));
				return null;
			}
		},
		[currentCollection]
	);

	const searchWords = useCallback(
		async (searchTerm: string) => {
			if (!currentCollection) {
				setError(new Error('No current collection selected'));
				return;
			}
			const { start, end } = loadingErrorHelper(() => {}, setError, 'wordsSearch');
			start();
			try {
				const url = new URL(
					`/api/collections/${currentCollection.id}/vocabulary/search`,
					window.location.origin
				);
				url.searchParams.set('term', searchTerm);

				const response = await fetch(url.toString());
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to search words');
				}

				const result = await response.json();
				setCurrentCollectionWords(result);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to search words'));
			}
		},
		[currentCollection]
	);

	const fetchCurrentCollectionWords = useCallback(async () => {
		if (!currentCollection) {
			setCurrentCollectionWords([]);
			return;
		}
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchWords');
		start();
		try {
			const response = await fetch(`/api/collections/${currentCollection.id}/vocabulary`);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to fetch collection words');
			}
			const result = await response.json();
			setCurrentCollectionWords(result as WordDetail[]);
			end();
		} catch (err) {
			end(
				err instanceof Error
					? err
					: new Error('Failed to fetch words for current collection')
			);
		}
	}, [currentCollection]);

	const getCurrentCollectionWordsToReview = useCallback(
		async (limit?: number) => {
			if (!currentCollection) {
				setError(new Error('No current collection selected'));
				return;
			}
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getWordsToReviewWords');
			start();
			try {
				const url = new URL(
					`/api/collections/${currentCollection.id}/vocabulary/review`,
					window.location.origin
				);
				if (limit) url.searchParams.set('limit', limit.toString());

				const response = await fetch(url.toString());
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to get words to review');
				}

				const result = await response.json();
				setCurrentCollectionWords(result as WordDetail[]);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to fetch words to review'));
			}
		},
		[currentCollection]
	);

	const refreshCurrentCollection = useCallback(async (): Promise<CollectionWithDetail | null> => {
		if (!currentCollection) return null;
		return await refreshCollection(currentCollection.id);
	}, [currentCollection, refreshCollection]);

	const bulkDeleteWordsFromCurrentCollection = useCallback(async (wordIds: string[]) => {
		if (!currentCollection) {
			setError(new Error('No current collection selected'));
			return;
		}
		const { start, end } = loadingErrorHelper(() => {}, setError, 'bulkDeleteWords');
		start();
		try {
			const response = await fetch(`/api/collections/${currentCollection.id}/vocabulary`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ wordIds }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to bulk delete words');
			}

			await fetchCurrentCollectionWords();
			await refreshCurrentCollection();
			end();
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to delete words'));
		}
	}, []);

	const fetchWordsByCollection = useCallback(async (collectionId: string) => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchWords');
		start();
		try {
			const response = await fetch(`/api/collections/${collectionId}/vocabulary`);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to fetch collection words');
			}
			const result = await response.json();
			if (collectionId === currentCollection?.id)
				setCurrentCollectionWords(result as WordDetail[]);
			end();
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to fetch words by collection'));
		}
	}, []);

	const getWordsToReviewWords = useCallback(async (collectionId: string, limit?: number) => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'getWordsToReviewWords');
		start();
		try {
			const url = new URL(
				`/api/collections/${collectionId}/vocabulary/review`,
				window.location.origin
			);
			if (limit) url.searchParams.set('limit', limit.toString());

			const response = await fetch(url.toString());
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to get words to review');
			}

			const result = await response.json();
			if (collectionId === currentCollection?.id)
				setCurrentCollectionWords(result as WordDetail[]);
			end();
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to fetch words to review'));
		}
	}, []);

	const bulkDeleteWords = useCallback(async (collectionId: string, wordIds: string[]) => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'bulkDeleteWords');
		start();
		try {
			const response = await fetch(`/api/collections/${collectionId}/vocabulary`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ wordIds }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to bulk delete words');
			}

			await fetchWordsByCollection(collectionId);
			end();
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to delete words'));
		}
	}, []);

	const getWordById = useCallback(async (id: string): Promise<WordDetail | null> => {
		try {
			const response = await fetch('/api/words/by-ids', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ wordIds: [id] }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to fetch word by ID');
			}

			const result = await response.json();
			return result[0] || null;
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to fetch word'));
			return null;
		}
	}, []);

	const fetchWord = useCallback(async (id: string): Promise<WordDetail | null> => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchWord');
		start();
		try {
			const response = await fetch('/api/words/by-ids', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ wordIds: [id] }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to fetch word by ID');
			}

			const result = await response.json();
			const word = result[0] || null;
			end();
			return word;
		} catch (err) {
			end(err instanceof Error ? err : new Error('Failed to fetch word'));
			return null;
		}
	}, []);

	const setCurrentCollectionById = useCallback(
		async (id: string): Promise<CollectionWithDetail | null> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'setCurrent');
			start();
			try {
				const existingCollection = collections.find((c) => c.id === id);
				if (existingCollection) {
					setCurrentCollection(existingCollection);
					setCurrentCollectionWords([]);
					end();
					return existingCollection;
				}

				const collection = await getCollection(id);
				if (collection) {
					setCurrentCollection(collection);
					setCurrentCollectionWords([]);
				}
				end();
				return collection;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to set current collection'));
				return null;
			}
		},
		[collections]
	);

	const clearCurrentCollection = useCallback(() => {
		setCurrentCollection(null);
		setCurrentCollectionWords([]);
	}, []);

	const addTermToCurrentCollection = useCallback(
		async (term: string, language: Language): Promise<CollectionWithDetail | null> => {
			if (!currentCollection) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await addTermToCollection(currentCollection.id, term, language);
		},
		[currentCollection]
	);

	const addWordsToCurrentCollection = useCallback(
		async (wordIds: string[]): Promise<CollectionWithDetail | null> => {
			if (!currentCollection) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await addWordsToCollection(currentCollection.id, wordIds);
		},
		[currentCollection]
	);

	const removeWordsFromCurrentCollection = useCallback(
		async (wordIds: string[]): Promise<CollectionWithDetail | null> => {
			if (!currentCollection) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await removeWordsFromCollection(currentCollection.id, wordIds);
		},
		[currentCollection]
	);

	const updateCurrentCollection = useCallback(
		async (data: { name?: string }): Promise<CollectionWithDetail | null> => {
			if (!currentCollection) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await updateCollection(currentCollection.id, data);
		},
		[currentCollection]
	);

	const deleteCurrentCollection = useCallback(async (): Promise<void> => {
		if (!currentCollection) {
			setError(new Error('No current collection selected'));
			return;
		}
		await deleteCollection(currentCollection.id);
	}, [currentCollection]);

	useEffect(() => {
		const loadInitialCollections = async () => {
			try {
				const cachedCollections = await getAllCollections();
				if (cachedCollections && cachedCollections.length > 0) {
					setCollections(cachedCollections);
				}
			} catch (cacheError) {
				console.warn('Failed to load collections from cache:', cacheError);
			}
			fetchCollections();
		};
		loadInitialCollections();
	}, []);

	return {
		loading,
		error,
		setError,

		// collections
		collections,
		searchCollections,
		createCollection,
		fetchCollections,
		deleteCollection,
		refreshCollection,
		updateCollection,
		getCollection,

		// current collection
		currentCollection,
		setCurrentCollection,
		setCurrentCollectionById,
		refreshCurrentCollection,
		clearCurrentCollection,
		updateCurrentCollection,
		deleteCurrentCollection,

		// words
		currentCollectionWords,
		fetchWord,
		searchWords,
		getWordById,
		bulkDeleteWords,
		addTermToCollection,
		addWordsToCollection,
		removeWordsFromCollection,
		fetchCurrentCollectionWords,
		fetchWordsByCollection,
		getWordsToReviewWords,
		getCurrentCollectionWordsToReview,
		bulkDeleteWordsFromCurrentCollection,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		getCollectionWordsToReview,
	};
}
