'use client';

import { useFloating<PERSON> } from '@/contexts/floating-ui-context';
import {
	DEFAULT_FLOATING_CONFIG,
	FloatingCoordinates,
	FloatingSettingsOptions,
	UseFloatingUIOptions,
	UseFloatingUIReturn,
} from '@/types/floating-ui';
import { createElement, useCallback, useEffect, useMemo, useRef } from 'react';

// ============================================================================
// MAIN FLOATING UI HOOK
// ============================================================================

export function useFloatingUIElement(
	id: string,
	content: React.ReactNode,
	options: UseFloatingUIOptions
): UseFloatingUIReturn {
	const {
		register,
		unregister,
		show: showElement,
		hide: hideElement,
		toggle: toggleElement,
		update: updateElement,
		getElement,
		checkCollisions,
		calculateOptimalPosition: _calculateOptimalPosition,
	} = useFloatingUI();

	const isRegistered = useRef(false);

	// Memoize the element configuration to prevent unnecessary re-registrations
	const elementConfig = useMemo(
		() => ({
			id,
			content,
			type: options.type,
			priority: options.priority || 'medium',
			position: options.position || 'bottom-right',
			coordinates: options.coordinates,
			dimensions: options.dimensions,
			zIndex: options.zIndex,
			persistent: options.persistent || false,
			collisionDetection: options.collisionDetection !== false,
			animation: options.animation,
			className: options.className,
			style: options.style,
			onShow: options.onShow,
			onHide: options.onHide,
			onCollision: options.onCollision,
		}),
		[
			id,
			content,
			options.type,
			options.priority,
			options.position,
			options.coordinates,
			options.dimensions,
			options.zIndex,
			options.persistent,
			options.collisionDetection,
			options.animation,
			options.className,
			options.style,
			options.onShow,
			options.onHide,
			options.onCollision,
		]
	);

	// Register element on mount
	useEffect(() => {
		if (!isRegistered.current) {
			register(elementConfig);
			isRegistered.current = true;

			// Auto-show if requested
			if (options.autoShow) {
				showElement(id);
			}
		}

		return () => {
			if (isRegistered.current) {
				unregister(id);
				isRegistered.current = false;
			}
		};
	}, [elementConfig, options.autoShow, register, unregister, showElement, id]);

	// Don't update content after registration to avoid infinite loops
	// Content should be stable or memoized by the caller

	// Disable options updates to prevent infinite loops
	// Options should be stable or the component should re-mount with new options

	// Memoized actions
	const show = useCallback(() => showElement(id), [showElement, id]);
	const hide = useCallback(() => hideElement(id), [hideElement, id]);
	const toggle = useCallback(() => toggleElement(id), [toggleElement, id]);
	const update = useCallback(
		(updates: Partial<any>) => updateElement(id, updates),
		[updateElement, id]
	);

	// Get current element state
	const element = getElement(id);
	const isVisible = element?.visible || false;

	// Calculate position
	const position = useMemo(() => {
		if (element?.coordinates) {
			return element.coordinates;
		}

		const positionDefaults = DEFAULT_FLOATING_CONFIG.positionDefaults;
		return (
			positionDefaults[options.position || 'bottom-right'] || positionDefaults['bottom-right']
		);
	}, [element?.coordinates, options.position]);

	// Get z-index
	const zIndex = useMemo(() => {
		if (element?.zIndex) return element.zIndex;

		const priorityZIndex = DEFAULT_FLOATING_CONFIG.priorityZIndex;
		return priorityZIndex[options.priority || 'medium'];
	}, [element?.zIndex, options.priority]);

	// Check collisions
	const collidingElementIds = checkCollisions(id);
	const hasCollisions = collidingElementIds.length > 0;
	const collidingElements = collidingElementIds
		.map((collidingId) => getElement(collidingId))
		.filter(Boolean) as any[];

	return {
		show,
		hide,
		toggle,
		update,
		isVisible,
		element,
		position,
		zIndex,
		hasCollisions,
		collidingElements,
	};
}

// ============================================================================
// FLOATING SETTINGS HOOK
// ============================================================================

export function useFloatingSettings(
	id: string = 'floating-settings',
	options: FloatingSettingsOptions = {}
) {
	// Settings content will be provided by the component using this hook
	const defaultContent = createElement(
		'div',
		{
			className: 'floating-settings-content p-4 bg-background border rounded-lg shadow-lg',
		},
		createElement(
			'h3',
			{
				className: 'text-lg font-semibold mb-2',
			},
			'Settings'
		)
	);

	return useFloatingUIElement(id, defaultContent, {
		type: 'settings',
		priority: 'high',
		position: 'bottom-right',
		coordinates: { bottom: 16, right: 16 },
		persistent: true,
		animation: { type: 'scale', duration: 200 },
		...options,
	});
}

// ============================================================================
// FLOATING NOTIFICATION HOOK
// ============================================================================

export function useFloatingNotification(
	id: string,
	content: React.ReactNode,
	options: Partial<UseFloatingUIOptions> = {}
) {
	return useFloatingUIElement(id, content, {
		type: 'notification',
		priority: 'high',
		position: 'top-right',
		coordinates: { top: 16, right: 16 },
		animation: { type: 'slide', duration: 250 },
		collisionDetection: true,
		...options,
	});
}

// ============================================================================
// FLOATING TOOLTIP HOOK
// ============================================================================

export function useFloatingTooltip(
	id: string,
	content: React.ReactNode,
	options: Partial<UseFloatingUIOptions> = {}
) {
	return useFloatingUIElement(id, content, {
		type: 'tooltip',
		priority: 'low',
		position: 'custom',
		animation: { type: 'fade', duration: 150 },
		collisionDetection: true,
		...options,
	});
}

// ============================================================================
// FLOATING MODAL HOOK
// ============================================================================

export function useFloatingModal(
	id: string,
	content: React.ReactNode,
	options: Partial<UseFloatingUIOptions> = {}
) {
	return useFloatingUIElement(id, content, {
		type: 'modal',
		priority: 'critical',
		position: 'center',
		coordinates: {
			top: 50,
			left: 50,
		},
		style: {
			transform: 'translate(-50%, -50%)',
		},
		persistent: true,
		animation: { type: 'scale', duration: 300 },
		...options,
	});
}

// ============================================================================
// FLOATING DROPDOWN HOOK
// ============================================================================

export function useFloatingDropdown(
	id: string,
	content: React.ReactNode,
	triggerRef: React.RefObject<HTMLElement>,
	options: Partial<UseFloatingUIOptions> = {}
) {
	// Calculate position relative to trigger element
	const calculateDropdownPosition = useCallback((): FloatingCoordinates => {
		if (!triggerRef.current) return { top: 0, left: 0 };

		const rect = triggerRef.current.getBoundingClientRect();
		return {
			top: rect.bottom + 8,
			left: rect.left,
		};
	}, [triggerRef]);

	const coordinates = useMemo(() => {
		return options.coordinates || calculateDropdownPosition();
	}, [options.coordinates, calculateDropdownPosition]);

	return useFloatingUIElement(id, content, {
		type: 'dropdown',
		priority: 'medium',
		position: 'custom',
		coordinates,
		animation: { type: 'slide', duration: 200 },
		collisionDetection: true,
		...options,
	});
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

export function useFloatingUIManager() {
	const {
		state,
		hideAll,
		showAll,
		getVisibleElements,
		getElementsByType,
		getElementsByPriority,
	} = useFloatingUI();

	const hideAllExcept = useCallback(
		(exceptIds: string[]) => {
			hideAll(exceptIds);
		},
		[hideAll]
	);

	const hideByType = useCallback(
		(type: any) => {
			const elements = getElementsByType(type);
			elements.forEach((element) => {
				if (element.visible) {
					hideAll([element.id]);
				}
			});
		},
		[getElementsByType, hideAll]
	);

	const showByType = useCallback(
		(type: any) => {
			const elements = getElementsByType(type);
			elements.forEach((element) => {
				if (!element.visible) {
					showAll();
				}
			});
		},
		[getElementsByType, showAll]
	);

	return {
		state,
		hideAll,
		showAll,
		hideAllExcept,
		hideByType,
		showByType,
		getVisibleElements,
		getElementsByType,
		getElementsByPriority,
		visibleCount: getVisibleElements().length,
		totalCount: state.elements.size,
	};
}

// ============================================================================
// RESPONSIVE FLOATING UI HOOK
// ============================================================================

export function useResponsiveFloatingUI(
	id: string,
	content: React.ReactNode,
	options: UseFloatingUIOptions & {
		mobileOptions?: Partial<UseFloatingUIOptions>;
		tabletOptions?: Partial<UseFloatingUIOptions>;
		desktopOptions?: Partial<UseFloatingUIOptions>;
	}
) {
	const { mobileOptions, tabletOptions, desktopOptions, ...baseOptions } = options;

	// Simple responsive detection (can be enhanced with proper media query hooks)
	const getResponsiveOptions = useCallback(() => {
		if (typeof window === 'undefined') return baseOptions;

		const width = window.innerWidth;

		if (width < DEFAULT_FLOATING_CONFIG.responsiveBreakpoints.mobile) {
			return { ...baseOptions, ...mobileOptions };
		} else if (width < DEFAULT_FLOATING_CONFIG.responsiveBreakpoints.tablet) {
			return { ...baseOptions, ...tabletOptions };
		} else {
			return { ...baseOptions, ...desktopOptions };
		}
	}, [baseOptions, mobileOptions, tabletOptions, desktopOptions]);

	const responsiveOptions = getResponsiveOptions();

	return useFloatingUIElement(id, content, responsiveOptions);
}
