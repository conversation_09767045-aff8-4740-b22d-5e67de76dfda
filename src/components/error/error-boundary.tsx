'use client';

import { Button } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { AppError, ErrorCategory, ErrorSeverity, errorLogger } from '@/lib/error-handling';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>gle, BookOpen, Home, RefreshCw, Search, Sparkles, Zap } from 'lucide-react';
import Link from 'next/link';
import { Component, ErrorInfo, ReactNode, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorBoundaryState {
	hasError: boolean;
	error: AppError | null;
	errorInfo: ErrorInfo | null;
}

interface ErrorBoundaryProps {
	children: ReactNode;
	fallback?: (error: AppError, errorInfo: ErrorInfo | null, retry: () => void) => ReactNode;
	onError?: (error: AppError, errorInfo: ErrorInfo) => void;
	level?: 'page' | 'section' | 'component';
	name?: string;
}

// ============================================================================
// ERROR BOUNDARY COMPONENT
// ============================================================================

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
	private retryTimeoutId: NodeJS.Timeout | null = null;

	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = {
			hasError: false,
			error: null,
			errorInfo: null,
		};
	}

	static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
		// Convert the error to AppError
		const appError = new AppError(
			error.message || 'An unexpected error occurred',
			'REACT_ERROR_BOUNDARY',
			500,
			ErrorSeverity.HIGH,
			ErrorCategory.CLIENT,
			{
				application: {
					component: 'ErrorBoundary',
					action: 'component_error',
				},
				additionalData: {
					originalError: error.name,
					stack: error.stack,
				},
			}
		);

		return {
			hasError: true,
			error: appError,
		};
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		const { onError, name } = this.props;

		// Create enhanced error with React error info
		const appError = new AppError(
			error.message || 'React component error',
			'REACT_COMPONENT_ERROR',
			500,
			ErrorSeverity.HIGH,
			ErrorCategory.CLIENT,
			{
				application: {
					component: name || 'ErrorBoundary',
					action: 'component_did_catch',
					state: {
						componentStack: errorInfo.componentStack,
					},
				},
				additionalData: {
					errorInfo,
					originalError: {
						name: error.name,
						message: error.message,
						stack: error.stack,
					},
				},
			}
		);

		// Log the error
		errorLogger.error(
			`React Error Boundary caught error in ${name || 'unknown component'}`,
			appError,
			{ errorInfo },
			'ErrorBoundary'
		);

		// Update state with error info
		this.setState({
			error: appError,
			errorInfo,
		});

		// Call custom error handler if provided
		if (onError) {
			onError(appError, errorInfo);
		}
	}

	componentWillUnmount() {
		if (this.retryTimeoutId) {
			clearTimeout(this.retryTimeoutId);
		}
	}

	retry = () => {
		// Clear the error state to retry rendering
		this.setState({
			hasError: false,
			error: null,
			errorInfo: null,
		});

		// Log the retry attempt
		errorLogger.info(
			`Error boundary retry attempted for ${this.props.name || 'unknown component'}`,
			{},
			'ErrorBoundary'
		);
	};

	render() {
		const { hasError, error, errorInfo } = this.state;
		const { children, fallback, level = 'component' } = this.props;

		if (hasError && error) {
			// Use custom fallback if provided
			if (fallback) {
				return fallback(error, errorInfo, this.retry);
			}

			// Default fallback based on level
			return this.renderDefaultFallback(error, errorInfo, level);
		}

		return children;
	}

	private renderDefaultFallback(error: AppError, _errorInfo: ErrorInfo | null, level: string) {
		switch (level) {
			case 'page':
				return <PageErrorFallback error={error} retry={this.retry} />;
			case 'section':
				return <SectionErrorFallback error={error} retry={this.retry} />;
			default:
				return <ComponentErrorFallback error={error} retry={this.retry} />;
		}
	}
}

// ============================================================================
// DEFAULT FALLBACK COMPONENTS
// ============================================================================

interface ErrorFallbackProps {
	error: AppError;
	retry: () => void;
}

function PageErrorFallback({ error, retry }: ErrorFallbackProps) {
	const { t } = useTranslation();
	const [randomFunFact] = useState(() => {
		const facts = t('errors.fun_facts') as unknown as string[];
		return facts[Math.floor(Math.random() * facts.length)];
	});

	if (typeof window === 'undefined') return null;

	return (
		<div className="min-h-screen flex items-center justify-center relative overflow-hidden">
			{/* Animated Background */}
			<div className="fixed inset-0 -z-10">
				<div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-destructive/10 animate-gradient-x" />
				<motion.div
					className="absolute inset-0 opacity-30"
					animate={{
						backgroundPosition: ['0% 0%', '100% 100%'],
					}}
					transition={{
						duration: 20,
						repeat: Infinity,
						repeatType: 'reverse',
					}}
					style={{
						backgroundImage:
							'radial-gradient(circle at 25% 25%, hsl(var(--primary)) 0%, transparent 50%), radial-gradient(circle at 75% 75%, hsl(var(--secondary)) 0%, transparent 50%)',
					}}
				/>
			</div>

			{/* Main Content */}
			<motion.div
				initial={{ opacity: 0, scale: 0.8 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ duration: 0.8, ease: 'easeOut' }}
				className="text-center space-y-8 max-w-2xl mx-auto px-6 relative z-10"
			>
				{/* Error Icon */}
				<motion.div
					initial={{ y: -50 }}
					animate={{ y: 0 }}
					transition={{ delay: 0.2, type: 'spring', stiffness: 100 }}
				>
					<div className="flex items-center justify-center w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-destructive/20 to-primary/20 rounded-full">
						<AlertTriangle className="h-12 w-12 text-destructive" />
					</div>
				</motion.div>

				{/* Animated Icons */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 0.5 }}
					className="flex justify-center space-x-4 mb-6"
				>
					{[BookOpen, Search, Sparkles, Zap].map((Icon, index) => (
						<motion.div
							key={index}
							animate={{
								y: [0, -10, 0],
								rotate: [0, 5, -5, 0],
								scale: [1, 1.1, 1],
							}}
							transition={{
								duration: 2,
								delay: index * 0.2,
								repeat: Infinity,
								repeatType: 'reverse',
							}}
							className="text-primary/60"
						>
							<Icon size={32} />
						</motion.div>
					))}
				</motion.div>

				{/* Error Message */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.7 }}
					className="space-y-4"
				>
					<h2 className="text-3xl md:text-4xl font-bold text-foreground">
						{t('errors.boundary_page_title')}
					</h2>
					<p className="text-lg text-muted-foreground max-w-md mx-auto leading-relaxed">
						{t('errors.boundary_page_description')}
					</p>
				</motion.div>

				{/* Action Buttons */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.9 }}
					className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6"
				>
					<Button
						size="lg"
						onClick={retry}
						className="group relative overflow-hidden bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg"
					>
						<motion.div
							className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
							animate={{ x: ['-100%', '100%'] }}
							transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
						/>
						<RefreshCw className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
						{t('errors.try_again')}
					</Button>

					<Button
						variant="outline"
						size="lg"
						onClick={() => window.location.reload()}
						className="group px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 hover:border-primary/50"
					>
						<RefreshCw className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
						{t('errors.refresh_page')}
					</Button>

					<Link href="/">
						<Button
							variant="outline"
							size="lg"
							className="group px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 hover:border-primary/50"
						>
							<Home className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
							{t('errors.go_home')}
						</Button>
					</Link>
				</motion.div>

				{/* Fun Fact */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 1.2 }}
					className="mt-12 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50"
				>
					<motion.div
						animate={{ rotate: [0, 5, -5, 0] }}
						transition={{ duration: 4, repeat: Infinity }}
						className="inline-block mb-2"
					>
						💡
					</motion.div>
					<p className="text-sm text-muted-foreground">
						<strong>{t('errors.fun_fact_prefix')}</strong> {randomFunFact}
					</p>
				</motion.div>

				{/* Development Details */}
				{process.env.NODE_ENV === 'development' && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 1.4 }}
						className="mt-6"
					>
						<details className="text-left bg-card/30 backdrop-blur-sm rounded-xl border border-border/30 p-4">
							<summary className="text-sm text-muted-foreground cursor-pointer hover:text-foreground transition-colors">
								{t('errors.development_details')}
							</summary>
							<pre className="mt-2 text-xs text-muted-foreground bg-muted/50 p-3 rounded overflow-auto max-h-40 font-mono">
								{JSON.stringify(error.toLogObject(), null, 2)}
							</pre>
						</details>
					</motion.div>
				)}
			</motion.div>
		</div>
	);
}

function SectionErrorFallback({ retry }: ErrorFallbackProps) {
	const { t } = useTranslation();

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5 }}
			className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 backdrop-blur-sm"
		>
			<div className="flex items-start space-x-4">
				<motion.div
					initial={{ scale: 0 }}
					animate={{ scale: 1 }}
					transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
					className="flex-shrink-0"
				>
					<div className="flex items-center justify-center w-12 h-12 bg-destructive/20 rounded-full">
						<AlertTriangle className="h-6 w-6 text-destructive" />
					</div>
				</motion.div>
				<div className="flex-1 min-w-0">
					<motion.h3
						initial={{ opacity: 0, x: -20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ delay: 0.3 }}
						className="text-lg font-semibold text-foreground mb-2"
					>
						{t('errors.boundary_section_title')}
					</motion.h3>
					<motion.p
						initial={{ opacity: 0, x: -20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ delay: 0.4 }}
						className="text-sm text-muted-foreground mb-4"
					>
						{t('errors.boundary_section_description')}
					</motion.p>
					<motion.div
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.5 }}
					>
						<Button
							size="sm"
							onClick={retry}
							variant="outline"
							className="group border-destructive/30 text-destructive hover:bg-destructive/10 hover:border-destructive/50"
						>
							<RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-12 transition-transform" />
							{t('errors.retry')}
						</Button>
					</motion.div>
				</div>
			</div>
		</motion.div>
	);
}

function ComponentErrorFallback({ retry }: ErrorFallbackProps) {
	const { t } = useTranslation();

	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			transition={{ duration: 0.3 }}
			className="bg-amber-50 dark:bg-amber-900/10 border border-amber-200 dark:border-amber-800 rounded-lg p-4"
		>
			<div className="flex items-center space-x-3">
				<motion.div
					initial={{ rotate: -10 }}
					animate={{ rotate: 0 }}
					transition={{ delay: 0.1, type: 'spring', stiffness: 200 }}
				>
					<AlertTriangle className="h-5 w-5 text-amber-500" />
				</motion.div>
				<span className="text-sm text-amber-800 dark:text-amber-200 flex-1 font-medium">
					{t('errors.boundary_component_title')}
				</span>
				<motion.div
					initial={{ opacity: 0, x: 10 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ delay: 0.2 }}
				>
					<Button
						size="sm"
						variant="outline"
						onClick={retry}
						className="text-xs border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20"
					>
						{t('errors.retry')}
					</Button>
				</motion.div>
			</div>
		</motion.div>
	);
}
