'use client';

import { Button } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { AppError, <PERSON>rrorCategory, getUserFriendlyMessage } from '@/lib/error-handling';
import { motion } from 'framer-motion';
import {
	AlertCircle,
	ArrowLeft,
	BookOpen,
	Bug,
	Home,
	RefreshCw,
	Search,
	Sparkles,
	WifiOff,
	Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ReactNode, useEffect, useState } from 'react';

// ============================================================================
// FLOATING WORDS AND ANIMATIONS
// ============================================================================

const errorFloatingWords = [
	'Error',
	'Oops',
	'Debug',
	'Fix',
	'Retry',
	'Vocab',
	'Learn',
	'Study',
	'Words',
	'Help',
];

// Pre-calculate random values to avoid hydration mismatch
const getRandomPositions = (words: string[]) => {
	return words.map(() => ({
		left: Math.random() * 100,
		xOffset: Math.random() * 200 - 100,
		xAnimate: Math.random() * 400 - 200,
		rotation: Math.random() * 360,
		repeatDelay: Math.random() * 5,
	}));
};

const FloatingWord = ({
	word,
	delay,
	position,
}: {
	word: string;
	delay: number;
	position: {
		left: number;
		xOffset: number;
		xAnimate: number;
		rotation: number;
		repeatDelay: number;
	};
}) => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 100, x: position.xOffset }}
			animate={{
				opacity: [0, 0.3, 0],
				y: [-100, -200, -300],
				x: position.xAnimate,
				rotate: position.rotation,
			}}
			transition={{
				duration: 8,
				delay,
				repeat: Infinity,
				repeatDelay: position.repeatDelay,
			}}
			className="absolute text-muted-foreground/20 font-mono text-sm pointer-events-none select-none"
			style={{
				left: `${position.left}%`,
				top: '100%',
			}}
		>
			{word}
		</motion.div>
	);
};

const GlitchText = ({ children }: { children: ReactNode }) => {
	const [isGlitching, setIsGlitching] = useState(false);

	useEffect(() => {
		const interval = setInterval(() => {
			setIsGlitching(true);
			setTimeout(() => setIsGlitching(false), 200);
		}, 3000);

		return () => clearInterval(interval);
	}, []);

	return (
		<motion.div
			className={`relative inline-block ${isGlitching ? 'animate-pulse' : ''}`}
			animate={
				isGlitching
					? {
							x: [0, -2, 2, -1, 1, 0],
							filter: [
								'hue-rotate(0deg)',
								'hue-rotate(90deg)',
								'hue-rotate(180deg)',
								'hue-rotate(270deg)',
								'hue-rotate(0deg)',
							],
					  }
					: {}
			}
			transition={{ duration: 0.2 }}
		>
			{children}
			{isGlitching && (
				<>
					<span className="absolute inset-0 text-red-500 opacity-70 -translate-x-1">
						{children}
					</span>
					<span className="absolute inset-0 text-blue-500 opacity-70 translate-x-1">
						{children}
					</span>
				</>
			)}
		</motion.div>
	);
};

// ============================================================================
// TYPES
// ============================================================================

interface ErrorPageProps {
	error?: AppError | Error | null;
	title?: string;
	description?: string;
	showRetry?: boolean;
	showGoHome?: boolean;
	showGoBack?: boolean;
	showReportBug?: boolean;
	onRetry?: () => void;
	onReportBug?: (error: AppError | Error) => void;
	className?: string;
}

interface OfflinePageProps {
	onRetry?: () => void;
}

interface NotFoundPageProps {
	onGoHome?: () => void;
}

// ============================================================================
// MAIN ERROR PAGE COMPONENT
// ============================================================================

export function ErrorPage({
	error,
	title,
	description,
	showRetry = true,
	showGoHome = true,
	showGoBack = true,
	showReportBug = false,
	onRetry,
	onReportBug,
	className = '',
}: ErrorPageProps) {
	const router = useRouter();
	const { t } = useTranslation();
	const [isRetrying, setIsRetrying] = useState(false);
	const [randomPositions] = useState(() => getRandomPositions(errorFloatingWords));
	const [randomFunFact] = useState(() => {
		const facts = t('errors.fun_facts') as unknown as string[];
		return facts[Math.floor(Math.random() * facts.length)];
	});

	// Determine error details
	const errorTitle = title || getErrorTitle(error, t);
	const errorDescription = description || getErrorDescription(error, t);
	const errorIcon = getErrorIcon(error);
	const canRetry = showRetry && (onRetry || error);

	const handleRetry = async () => {
		if (isRetrying) return;

		setIsRetrying(true);
		try {
			if (onRetry) {
				await Promise.resolve(onRetry());
			} else {
				// Default retry: reload the page
				window.location.reload();
			}
		} finally {
			setIsRetrying(false);
		}
	};

	const handleGoBack = () => {
		if (window.history.length > 1) {
			router.back();
		} else {
			router.push('/');
		}
	};

	const handleReportBug = () => {
		if (onReportBug && error) {
			onReportBug(error);
		}
	};

	if (typeof window === 'undefined') return null;

	return (
		<div
			className={`min-h-screen flex items-center justify-center relative overflow-hidden ${className}`}
		>
			{/* Animated Background */}
			<div className="fixed inset-0 -z-10">
				<div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-destructive/10 animate-gradient-x" />
				<motion.div
					className="absolute inset-0 opacity-30"
					animate={{
						backgroundPosition: ['0% 0%', '100% 100%'],
					}}
					transition={{
						duration: 20,
						repeat: Infinity,
						repeatType: 'reverse',
					}}
					style={{
						backgroundImage:
							'radial-gradient(circle at 25% 25%, hsl(var(--primary)) 0%, transparent 50%), radial-gradient(circle at 75% 75%, hsl(var(--secondary)) 0%, transparent 50%)',
					}}
				/>
			</div>

			{/* Floating Words */}
			{errorFloatingWords.map((word, index) => (
				<FloatingWord
					key={`${word}-${index}`}
					word={word}
					delay={index * 0.8}
					position={randomPositions[index]}
				/>
			))}

			{/* Main Content */}
			<motion.div
				initial={{ opacity: 0, scale: 0.8 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ duration: 0.8, ease: 'easeOut' }}
				className="text-center space-y-8 max-w-2xl mx-auto px-6 relative z-10"
			>
				{/* Error Icon with Glitch Effect */}
				<motion.div
					initial={{ y: -50 }}
					animate={{ y: 0 }}
					transition={{ delay: 0.2, type: 'spring', stiffness: 100 }}
				>
					<GlitchText>
						<div className="flex items-center justify-center w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-destructive/20 to-primary/20 rounded-full">
							{errorIcon}
						</div>
					</GlitchText>
				</motion.div>

				{/* Animated Icons */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 0.5 }}
					className="flex justify-center space-x-4 mb-6"
				>
					{[BookOpen, Search, Sparkles, Zap].map((Icon, index) => (
						<motion.div
							key={index}
							animate={{
								y: [0, -10, 0],
								rotate: [0, 5, -5, 0],
								scale: [1, 1.1, 1],
							}}
							transition={{
								duration: 2,
								delay: index * 0.2,
								repeat: Infinity,
								repeatType: 'reverse',
							}}
							className="text-primary/60"
						>
							<Icon size={32} />
						</motion.div>
					))}
				</motion.div>

				{/* Error Message */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.7 }}
					className="space-y-4"
				>
					<h2 className="text-3xl md:text-4xl font-bold text-foreground">{errorTitle}</h2>
					<p className="text-lg text-muted-foreground max-w-md mx-auto leading-relaxed">
						{errorDescription}
					</p>
				</motion.div>

				{/* Error ID (for debugging) */}
				{error instanceof AppError && error.id && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 0.8 }}
						className="text-xs text-muted-foreground/70 font-mono"
					>
						{t('errors.error_id')}: {error.id}
					</motion.div>
				)}

				{/* Action Buttons */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.9 }}
					className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6"
				>
					{canRetry && (
						<Button
							size="lg"
							onClick={handleRetry}
							disabled={isRetrying}
							className="group relative overflow-hidden bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg"
						>
							<motion.div
								className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
								animate={{ x: ['-100%', '100%'] }}
								transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
							/>
							<RefreshCw
								className={`mr-2 h-5 w-5 ${
									isRetrying ? 'animate-spin' : 'group-hover:rotate-12'
								} transition-transform`}
							/>
							{isRetrying ? t('errors.retrying') : t('errors.try_again')}
						</Button>
					)}

					<div className="flex gap-4">
						{showGoBack && (
							<Button
								variant="outline"
								size="lg"
								onClick={handleGoBack}
								className="group px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 hover:border-primary/50"
							>
								<ArrowLeft className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
								{t('errors.go_back')}
							</Button>
						)}

						{showGoHome && (
							<Link href="/">
								<Button
									variant="outline"
									size="lg"
									className="group px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 hover:border-primary/50"
								>
									<Home className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
									{t('errors.go_home')}
								</Button>
							</Link>
						)}
					</div>

					{showReportBug && error && (
						<Button
							variant="outline"
							size="lg"
							onClick={handleReportBug}
							className="group px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 hover:border-destructive/50 text-destructive hover:text-destructive"
						>
							<Bug className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
							{t('errors.report_bug')}
						</Button>
					)}
				</motion.div>

				{/* Fun Fact */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 1.2 }}
					className="mt-12 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50"
				>
					<motion.div
						animate={{ rotate: [0, 5, -5, 0] }}
						transition={{ duration: 4, repeat: Infinity }}
						className="inline-block mb-2"
					>
						💡
					</motion.div>
					<p className="text-sm text-muted-foreground">
						<strong>{t('errors.fun_fact_prefix')}</strong> {randomFunFact}
					</p>
				</motion.div>

				{/* Development Details */}
				{process.env.NODE_ENV === 'development' && error instanceof AppError && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 1.4 }}
						className="mt-6"
					>
						<details className="text-left bg-card/30 backdrop-blur-sm rounded-xl border border-border/30 p-4">
							<summary className="text-sm text-muted-foreground cursor-pointer hover:text-foreground transition-colors">
								{t('errors.development_details')}
							</summary>
							<pre className="mt-2 text-xs text-muted-foreground bg-muted/50 p-3 rounded overflow-auto max-h-40 font-mono">
								{JSON.stringify(error.toLogObject(), null, 2)}
							</pre>
						</details>
					</motion.div>
				)}
			</motion.div>
		</div>
	);
}

// ============================================================================
// SPECIALIZED ERROR PAGES
// ============================================================================

export function OfflinePage({ onRetry }: OfflinePageProps) {
	const { t } = useTranslation();
	return (
		<ErrorPage
			title={t('errors.offline_title')}
			description={t('errors.offline_description')}
			onRetry={onRetry}
			showGoHome={false}
			showGoBack={false}
		/>
	);
}

export function NotFoundPage({ onGoHome }: NotFoundPageProps) {
	const { t } = useTranslation();
	return (
		<ErrorPage
			title={t('errors.404_title')}
			description={t('errors.404_description')}
			showRetry={false}
			showGoHome={true}
			showGoBack={true}
			onRetry={onGoHome}
		/>
	);
}

export function ServerErrorPage({ onRetry }: { onRetry?: () => void }) {
	const { t } = useTranslation();
	return (
		<ErrorPage
			title={t('errors.server_title')}
			description={t('errors.server_description')}
			onRetry={onRetry}
			showReportBug={true}
		/>
	);
}

export function MaintenancePage() {
	const { t } = useTranslation();
	return (
		<ErrorPage
			title={t('errors.maintenance_title')}
			description={t('errors.maintenance_description')}
			showRetry={true}
			showGoHome={false}
			showGoBack={false}
		/>
	);
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function getErrorTitle(error?: AppError | Error | null, t?: (key: string) => string): string {
	if (!t) return 'Something went wrong';
	if (!error) return t('errors.general_title');

	if (error instanceof AppError) {
		switch (error.category) {
			case ErrorCategory.NETWORK:
				return t('errors.network_title');
			case ErrorCategory.AUTHENTICATION:
				return t('errors.auth_title');
			case ErrorCategory.AUTHORIZATION:
				return t('errors.access_denied_title');
			case ErrorCategory.NOT_FOUND:
				return t('errors.404_title');
			case ErrorCategory.VALIDATION:
				return t('errors.validation_title');
			case ErrorCategory.SERVER:
				return t('errors.server_title');
			default:
				return t('errors.general_title');
		}
	}

	return t('errors.general_title');
}

function getErrorDescription(error?: AppError | Error | null, t?: (key: string) => string): string {
	if (!t) return 'An unexpected error occurred. Please try again.';
	if (!error) return t('errors.general_description');

	if (error instanceof AppError) {
		switch (error.category) {
			case ErrorCategory.NETWORK:
				return t('errors.network_description');
			case ErrorCategory.AUTHENTICATION:
				return t('errors.auth_description');
			case ErrorCategory.AUTHORIZATION:
				return t('errors.access_denied_description');
			case ErrorCategory.NOT_FOUND:
				return t('errors.404_description');
			case ErrorCategory.VALIDATION:
				return t('errors.validation_description');
			case ErrorCategory.SERVER:
				return t('errors.server_description');
			default:
				return getUserFriendlyMessage(error);
		}
	}

	return error.message || t('errors.general_description');
}

function getErrorIcon(error?: AppError | Error | null) {
	if (!error) return <AlertCircle className="h-8 w-8 text-red-500" />;

	if (error instanceof AppError) {
		switch (error.category) {
			case ErrorCategory.NETWORK:
				return <WifiOff className="h-8 w-8 text-red-500" />;
			case ErrorCategory.AUTHENTICATION:
			case ErrorCategory.AUTHORIZATION:
				return <AlertCircle className="h-8 w-8 text-yellow-500" />;
			default:
				return <AlertCircle className="h-8 w-8 text-red-500" />;
		}
	}

	return <AlertCircle className="h-8 w-8 text-red-500" />;
}
