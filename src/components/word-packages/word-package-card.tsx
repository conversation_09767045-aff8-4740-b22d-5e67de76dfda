'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Users, Star, Plus } from 'lucide-react';
import { WordPackageWithStats } from '@/models/word-package';
import { Language, Difficulty } from '@prisma/client';

interface WordPackageCardProps {
	wordPackage: WordPackageWithStats;
	onSelect: (packageId: string) => void;
	isSelecting?: boolean;
	disabled?: boolean;
}

const difficultyColors = {
	[Difficulty.BEGINNER]: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
	[Difficulty.INTERMEDIATE]:
		'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
	[Difficulty.ADVANCED]: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const languageLabels = {
	[Language.EN]: 'English',
	[Language.VI]: 'Vietnamese',
};

export function WordPackageCard({
	wordPackage,
	onSelect,
	isSelecting = false,
	disabled = false,
}: WordPackageCardProps) {
	const handleSelect = () => {
		if (!disabled && !isSelecting) {
			onSelect(wordPackage.id);
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.2 }}
		>
			<Card className="h-full hover:shadow-lg transition-shadow duration-200 border-2 hover:border-primary/20">
				<CardHeader className="pb-3">
					<div className="flex items-start justify-between gap-2">
						<div className="flex-1 min-w-0">
							<CardTitle className="text-lg font-semibold line-clamp-2">
								{wordPackage.name}
							</CardTitle>
							<CardDescription className="mt-1 line-clamp-2">
								{wordPackage.description}
							</CardDescription>
						</div>
						<Badge
							variant="secondary"
							className={difficultyColors[wordPackage.difficulty]}
						>
							{wordPackage.difficulty}
						</Badge>
					</div>
				</CardHeader>

				<CardContent className="pt-0">
					<div className="space-y-4">
						{/* Stats */}
						<div className="flex items-center gap-4 text-sm text-muted-foreground">
							<div className="flex items-center gap-1">
								<BookOpen className="h-4 w-4" />
								<span>{wordPackage.word_count} words</span>
							</div>
							<div className="flex items-center gap-1">
								<Users className="h-4 w-4" />
								<span>{wordPackage._count.user_selections} users</span>
							</div>
						</div>

						{/* Language and Category */}
						<div className="flex items-center gap-2">
							<Badge variant="outline">
								{languageLabels[wordPackage.source_language as Language]}
							</Badge>
							<Badge variant="outline">{wordPackage.category}</Badge>
						</div>

						{/* Tags */}
						{wordPackage.tags.length > 0 && (
							<div className="flex flex-wrap gap-1">
								{wordPackage.tags.slice(0, 3).map((tag, index) => (
									<Badge key={index} variant="secondary" className="text-xs">
										{tag}
									</Badge>
								))}
								{wordPackage.tags.length > 3 && (
									<Badge variant="secondary" className="text-xs">
										+{wordPackage.tags.length - 3}
									</Badge>
								)}
							</div>
						)}

						{/* Select Button */}
						<Button
							onClick={handleSelect}
							disabled={disabled || isSelecting}
							className="w-full"
							size="sm"
						>
							{isSelecting ? (
								<>
									<motion.div
										animate={{ rotate: 360 }}
										transition={{
											duration: 1,
											repeat: Infinity,
											ease: 'linear',
										}}
										className="mr-2"
									>
										<Star className="h-4 w-4" />
									</motion.div>
									Adding...
								</>
							) : (
								<>
									<Plus className="h-4 w-4 mr-2" />
									Add to Collection
								</>
							)}
						</Button>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
