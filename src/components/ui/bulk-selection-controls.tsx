'use client';

import { But<PERSON> } from './button';
import { Translate } from './translate';
import { Trash2 } from 'lucide-react';

interface BulkSelectionControlsProps {
	selectedCount: number;
	totalCount: number;
	onSelectAll: () => void;
	onDeselectAll: () => void;
	onDeleteSelected: () => void;
	isDeleting?: boolean;
	className?: string;
}

export function BulkSelectionControls({
	selectedCount,
	totalCount,
	onSelectAll,
	onDeselectAll,
	onDeleteSelected,
	isDeleting = false,
	className = '',
}: BulkSelectionControlsProps) {
	const allSelected = selectedCount === totalCount && totalCount > 0;
	const someSelected = selectedCount > 0 && selectedCount < totalCount;

	return (
		<div
			className={`sticky top-4 z-10 bg-background/95 backdrop-blur-sm border border-border rounded-lg p-4 shadow-lg ${className}`}
		>
			<div className="flex items-center justify-between gap-4">
				<div className="flex items-center gap-4">
					<Button
						variant="outline"
						size="sm"
						onClick={allSelected ? onDeselectAll : onSelectAll}
						disabled={isDeleting}
					>
						{allSelected ? (
							<Translate text="words.bulk_delete.deselect_all" />
						) : (
							<Translate text="words.bulk_delete.select_all" />
						)}
					</Button>

					<div className="text-sm text-muted-foreground">
						<Translate
							text="words.bulk_delete.selected_count"
							values={{ count: selectedCount, total: totalCount }}
						/>
					</div>
				</div>

				<div className="flex items-center gap-2">
					{selectedCount > 0 && (
						<Button
							variant="destructive"
							size="sm"
							onClick={onDeleteSelected}
							disabled={isDeleting}
							className="flex items-center gap-2"
						>
							<Trash2 size={16} />
							{isDeleting ? (
								<Translate text="words.bulk_delete.deleting" />
							) : (
								<Translate text="words.bulk_delete.delete_selected" />
							)}
						</Button>
					)}
				</div>
			</div>
		</div>
	);
}
