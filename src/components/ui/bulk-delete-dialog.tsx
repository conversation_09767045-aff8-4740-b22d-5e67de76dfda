'use client';

import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	<PERSON><PERSON>Header,
	DialogTitle,
} from './dialog';
import { Button } from './button';
import { Translate } from './translate';
import { Trash2 } from 'lucide-react';

interface BulkDeleteDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	selectedCount: number;
	onConfirm: () => void;
	isDeleting?: boolean;
}

export function BulkDeleteDialog({
	open,
	onOpenChange,
	selectedCount,
	onConfirm,
	isDeleting = false,
}: BulkDeleteDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2 text-destructive">
						<Trash2 size={20} />
						<Translate text="words.bulk_delete.confirm_title" />
					</DialogTitle>
					<DialogDescription>
						<Translate
							text="words.bulk_delete.confirm_message"
							values={{ count: selectedCount }}
						/>
					</DialogDescription>
				</DialogHeader>
				<DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={isDeleting}
					>
						<Translate text="ui.cancel" />
					</Button>
					<Button
						variant="destructive"
						onClick={onConfirm}
						disabled={isDeleting}
						className="flex items-center gap-2"
					>
						<Trash2 size={16} />
						{isDeleting ? (
							<Translate text="words.bulk_delete.deleting" />
						) : (
							<Translate text="words.bulk_delete.delete_selected" />
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
