'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/contexts/admin-auth-context';
import { LoadingSpinner } from '@/components/ui';

// ============================================================================
// TYPES
// ============================================================================

interface AdminGuardProps {
	children: ReactNode;
	fallback?: ReactNode;
	redirectTo?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function AdminGuard({ children, fallback, redirectTo = '/admin/login' }: AdminGuardProps) {
	const { isLoading, isAuthenticated, isAdmin, user } = useAdminAuth();
	const router = useRouter();

	useEffect(() => {
		// Don't redirect while loading
		if (isLoading) return;

		// If not authenticated, redirect to login
		if (!isAuthenticated) {
			console.log('AdminGuard: Not authenticated, redirecting to login');
			router.push(redirectTo);
			return;
		}

		// If authenticated but not admin, redirect to login with error
		if (!isAdmin) {
			console.log('AdminGuard: Not admin or account disabled, redirecting to login');
			router.push(`${redirectTo}?error=access_denied`);
			return;
		}

		// If user is disabled, redirect to login
		if (user?.disabled) {
			console.log('AdminGuard: Account disabled, redirecting to login');
			router.push(`${redirectTo}?error=account_disabled`);
			return;
		}
	}, [isLoading, isAuthenticated, isAdmin, user?.disabled, router, redirectTo]);

	// Show loading spinner while checking authentication or if access denied
	if (isLoading || !isAuthenticated || !isAdmin || user?.disabled) {
		return (
			fallback || (
				<div className="min-h-screen flex items-center justify-center">
					<LoadingSpinner size="lg" />
				</div>
			)
		);
	}

	// Render children if all checks pass
	return <>{children}</>;
}

// ============================================================================
// HIGHER-ORDER COMPONENT
// ============================================================================

/**
 * Higher-order component that wraps a component with AdminGuard
 */
export function withAdminGuard<P extends object>(
	Component: React.ComponentType<P>,
	options?: {
		fallback?: ReactNode;
		redirectTo?: string;
	}
) {
	const WrappedComponent = (props: P) => {
		return (
			<AdminGuard fallback={options?.fallback} redirectTo={options?.redirectTo}>
				<Component {...props} />
			</AdminGuard>
		);
	};

	WrappedComponent.displayName = `withAdminGuard(${Component.displayName || Component.name})`;

	return WrappedComponent;
}

// ============================================================================
// HOOK FOR CONDITIONAL RENDERING
// ============================================================================

/**
 * Hook to check admin access for conditional rendering
 */
export function useAdminGuard() {
	const { isLoading, isAuthenticated, isAdmin, user } = useAdminAuth();

	const hasAccess = isAuthenticated && isAdmin && !user?.disabled;
	const shouldRedirect = !isLoading && (!isAuthenticated || !isAdmin || user?.disabled);

	return {
		isLoading,
		hasAccess,
		shouldRedirect,
		isAuthenticated,
		isAdmin,
		isDisabled: user?.disabled || false,
	};
}
