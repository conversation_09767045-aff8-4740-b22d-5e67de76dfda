import { UserRepository } from '@/backend/repositories';
import { UserWithDetail } from '@/models/user';
import { Prisma, Provider, Role } from '@prisma/client';
import { AuditHelper } from '@/backend/utils/audit.helper';
import { AUDIT_ACTIONS } from './audit.service';

export interface UserService {
	getUserById(userId: string): Promise<UserWithDetail | null>;
	getUserByProviderId(provider: Provider, providerId: string): Promise<UserWithDetail | null>;
	getUserByUsername(username: string): Promise<UserWithDetail | null>;
	createUser(
		data: {
			provider: Provider;
			provider_id: string;
			email?: string;
			name?: string;
			role?: Role;
		},
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	createUserWithPassword(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
			role?: Role;
		},
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	createAdminUser(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	updateUserRole(
		userId: string,
		role: Role,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	updatePassword(userId: string, passwordHash: string): Promise<UserWithDetail>;
	getAllUsers(limit?: number, offset?: number): Promise<UserWithDetail[]>;
	getAllUsersWithFilters(
		limit?: number,
		offset?: number,
		search?: string,
		filters?: {
			provider?: Provider;
			status?: 'active' | 'disabled';
			role?: Role;
			sortBy?: 'created_at' | 'username';
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<UserWithDetail[]>;
	getUsersCount(): Promise<number>;
	getUsersCountWithFilters(
		search?: string,
		filters?: {
			provider?: Provider;
			status?: 'active' | 'disabled';
			role?: Role;
		}
	): Promise<number>;
	disableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	enableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	deleteUser(userId: string): Promise<void>;
	updateUserProfile(
		userId: string,
		data: { email?: string; name?: string }
	): Promise<UserWithDetail>;
	changeUserPassword(userId: string, passwordHash: string): Promise<void>;
	getAdminCount(): Promise<number>;
}

export class UserServiceImpl implements UserService {
	constructor(
		private readonly getUserRepository: () => UserRepository,
		private readonly getAuditHelper: () => AuditHelper
	) {}

	async getUserById(userId: string): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findById(userId);
		return user as UserWithDetail | null;
	}

	async getUserByProviderId(
		provider: Provider,
		providerId: string
	): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findByProviderId(provider, providerId);
		return user as UserWithDetail | null;
	}

	async getUserByUsername(username: string): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findByUsername(username);
		return user as UserWithDetail | null;
	}

	async createUser(
		data: {
			provider: Provider;
			provider_id: string;
			email?: string;
			name?: string;
			role?: Role;
		},
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			role: data.role || Role.USER,
			disabled: false,
		} as Prisma.UserCreateInput);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.USER_CREATED,
				user.id,
				undefined, // no admin for regular user creation
				{
					provider: data.provider,
					provider_id: data.provider_id,
					email: data.email,
					name: data.name,
					role: user.role,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for user creation:', error);
		}

		return user as UserWithDetail;
	}

	async createUserWithPassword(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
			role?: Role;
		},
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			role: data.role || Role.USER,
			disabled: false,
		} as Prisma.UserCreateInput);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.USER_CREATED,
				user.id,
				undefined,
				{
					username: data.username,
					provider: data.provider,
					role: user.role,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for user creation:', error);
		}

		return user as UserWithDetail;
	}

	async createAdminUser(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			role: Role.ADMIN,
			disabled: false,
		} as Prisma.UserCreateInput);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.ADMIN_USER_CREATED,
				user.id,
				adminId,
				{
					username: data.username,
					provider: data.provider,
					role: Role.ADMIN,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for admin user creation:', error);
		}

		return user as UserWithDetail;
	}

	async updateUserRole(
		userId: string,
		role: Role,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		const currentUser = await this.getUserRepository().findById(userId);
		const user = await this.getUserRepository().update(userId, {
			role,
		} as Prisma.UserUpdateInput);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.USER_ROLE_CHANGED,
				userId,
				adminId,
				{
					old_role: currentUser?.role,
					new_role: role,
					admin_action: !!adminId,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for role change:', error);
		}

		return user as UserWithDetail;
	}

	async updatePassword(userId: string, passwordHash: string): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			password_hash: passwordHash,
		} as Prisma.UserUpdateInput);
		return user as UserWithDetail;
	}

	async getAllUsers(limit: number = 50, offset: number = 0): Promise<UserWithDetail[]> {
		const users = await this.getUserRepository().findWithPagination({}, { limit, offset });
		return users as UserWithDetail[];
	}

	async getUsersCount(): Promise<number> {
		return await this.getUserRepository().count();
	}

	async getAllUsersWithFilters(
		limit: number = 50,
		offset: number = 0,
		search?: string,
		filters?: {
			provider?: Provider;
			status?: 'active' | 'disabled';
			role?: Role;
			sortBy?: 'created_at' | 'username';
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<UserWithDetail[]> {
		const where: any = {};

		// Apply search filter
		if (search) {
			where.OR = [
				{ username: { contains: search, mode: 'insensitive' } },
				{ provider_id: { contains: search, mode: 'insensitive' } },
				{ email: { contains: search, mode: 'insensitive' } },
				{ name: { contains: search, mode: 'insensitive' } },
			];
		}

		// Apply provider filter
		if (filters?.provider) {
			where.provider = filters.provider;
		}

		// Apply status filter
		if (filters?.status) {
			where.disabled = filters.status === 'disabled';
		}

		// Apply role filter
		if (filters?.role) {
			where.role = filters.role;
		}

		// Determine sort order
		const orderBy: any = {};
		const sortField = filters?.sortBy || 'created_at';
		const sortDirection = filters?.sortOrder || 'desc';
		orderBy[sortField] = sortDirection;

		const users = await this.getUserRepository().findWithPagination(where, {
			limit,
			offset,
			orderBy,
		});
		return users as UserWithDetail[];
	}

	async getUsersCountWithFilters(
		search?: string,
		filters?: {
			provider?: Provider;
			status?: 'active' | 'disabled';
			role?: Role;
		}
	): Promise<number> {
		const where: any = {};

		// Apply search filter
		if (search) {
			where.OR = [
				{ username: { contains: search, mode: 'insensitive' } },
				{ provider_id: { contains: search, mode: 'insensitive' } },
				{ email: { contains: search, mode: 'insensitive' } },
				{ name: { contains: search, mode: 'insensitive' } },
			];
		}

		// Apply provider filter
		if (filters?.provider) {
			where.provider = filters.provider;
		}

		// Apply status filter
		if (filters?.status) {
			where.disabled = filters.status === 'disabled';
		}

		// Apply role filter
		if (filters?.role) {
			where.role = filters.role;
		}

		return await this.getUserRepository().count(where);
	}

	async disableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			disabled: true,
		} as Prisma.UserUpdateInput);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.USER_DISABLED,
				userId,
				adminId,
				{
					admin_action: !!adminId,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for user disable:', error);
		}

		return user as UserWithDetail;
	}

	async enableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			disabled: false,
		} as Prisma.UserUpdateInput);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.USER_ENABLED,
				userId,
				adminId,
				{
					admin_action: !!adminId,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for user enable:', error);
		}

		return user as UserWithDetail;
	}

	async deleteUser(userId: string): Promise<void> {
		await this.getUserRepository().delete({ id: userId });
	}

	async updateUserProfile(
		userId: string,
		data: { email?: string; name?: string }
	): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, data as Prisma.UserUpdateInput);
		return user as UserWithDetail;
	}

	async changeUserPassword(userId: string, passwordHash: string): Promise<void> {
		await this.getUserRepository().update(userId, {
			password_hash: passwordHash,
		} as Prisma.UserUpdateInput);
	}

	async getAdminCount(): Promise<number> {
		return await this.getUserRepository().count({
			role: Role.ADMIN,
		});
	}
}
