import { UserService } from './user.service';
import { FeedbackService } from './feedback.service';
import { BaseRepository } from '@/backend/repositories/base.repository';
import { Feedback } from '@prisma/client';
import { ICacheService } from './cache-factory.service';
import { TokenMonitorService } from './token-monitor.service';
import { AuditService, AUDIT_ACTIONS } from './audit.service';
import { AuditHelper } from '@/backend/utils/audit.helper';
import { CollectionService } from './collection.service';
import { UserWithDetail } from '@/models/user';
import { Role, Provider } from '@prisma/client';
import bcrypt from 'bcryptjs';

export interface AdminStats {
	users: {
		total: number;
		active: number;
		admins: number;
		newThisWeek: number;
		newToday: number;
		disabled: number;
	};
	collections: {
		total: number;
		public: number;
		private: number;
	};
	words: {
		total: number;
		byLanguage: Record<string, number>;
	};
	feedback: {
		total: number;
		pending: number;
		resolved: number;
		newThisWeek: number;
	};
	practice: {
		totalSessions: number;
		activeUsersToday: number;
		averageSessionDuration: number;
		completionRate: number;
	};
	llm: {
		totalRequests: number;
		totalTokensUsed: number;
		totalCost: number;
		requestsToday: number;
		tokensToday: number;
		costToday: number;
		averageResponseTime: number;
		errorRate: number;
		topModels: Array<{ model: string; usage: number; cost: number }>;
	};
	cache: {
		hitRate: number;
		missRate: number;
		totalKeys: number;
		memoryUsage: number;
		evictions: number;
		averageResponseTime: number;
		topCachedResources: Array<{ resource: string; hits: number; hitRate: number }>;
	};
	system: {
		cacheHitRate: number;
		apiResponseTime: number;
		errorRate: number;
		uptime: number;
		memoryUsage: number;
		cpuUsage: number;
		diskUsage: number;
	};
}

export interface AdminService {
	// Dashboard & Stats
	getAdminStats(): Promise<AdminStats>;
	getSystemHealth(): Promise<{ status: string; details: Record<string, any> }>;

	// User Management
	getAllUsers(
		page: number,
		limit: number,
		search?: string,
		filters?: {
			provider?: Provider;
			status?: 'active' | 'disabled';
			role?: Role;
			sortBy?: 'created_at' | 'username';
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<{
		users: UserWithDetail[];
		total: number;
		page: number;
		limit: number;
	}>;
	getUsersCount(search?: string): Promise<number>;
	getUserById(userId: string): Promise<UserWithDetail | null>;
	updateUserRole(
		userId: string,
		role: Role,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	disableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	enableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	createAdminUser(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	createUser(
		data: {
			provider: Provider;
			provider_id: string;
			email?: string;
			name?: string;
			role: Role;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	createUserWithPassword(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
			role: Role;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail>;
	deleteUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void>;
	bulkDeleteUsers(
		userIds: string[],
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<{ success: number; failed: number; errors: string[] }>;
	updateUserProfile(
		userId: string,
		data: { email?: string; name?: string }
	): Promise<UserWithDetail>;
	changeUserPassword(
		userId: string,
		passwordHash: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void>;
	getAdminCount(): Promise<number>;

	// Feedback Management
	getAllFeedback(
		page?: number,
		limit?: number,
		search?: string,
		status?: string
	): Promise<{
		feedbacks: any[];
		total: number;
		page: number;
		limit: number;
	}>;
	deleteFeedback(
		feedbackId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void>;
	bulkDeleteFeedback(
		feedbackIds: string[],
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<{ success: number; failed: number; errors: string[] }>;
	updateFeedbackStatus(
		feedbackId: string,
		status: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<any>;

	// System Management
	clearCache(
		cacheType?: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<{ success: boolean; message: string }>;
	getTokenUsageStats(): Promise<any>;

	// Audit & Monitoring
	getRecentActivity(limit: number): Promise<any[]>;
	getErrorLogs(limit: number): Promise<any[]>;
}

export class AdminServiceImpl implements AdminService {
	constructor(
		private readonly getUserService: () => UserService,
		private readonly getFeedbackService: () => FeedbackService,
		private readonly getFeedbackRepository: () => BaseRepository<Feedback>,
		private readonly getCacheService: () => Promise<ICacheService>,
		private readonly getTokenMonitorService: () => TokenMonitorService,
		private readonly getAuditService: () => AuditService,
		private readonly getAuditHelper: () => AuditHelper,
		private readonly getCollectionService: () => CollectionService
	) {}

	async getAdminStats(): Promise<AdminStats> {
		const userService = this.getUserService();
		const feedbackService = this.getFeedbackService();
		const tokenMonitorService = this.getTokenMonitorService();

		// Get user stats
		const totalUsers = await userService.getUsersCount();
		const allUsers = await userService.getAllUsers(1000, 0); // Get all users for analysis
		const activeUsers = allUsers.filter((user) => !user.disabled).length;
		const disabledUsers = allUsers.filter((user) => user.disabled).length;
		const adminUsers = allUsers.filter((user) => user.role === Role.ADMIN).length;

		const oneWeekAgo = new Date();
		oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
		const oneDayAgo = new Date();
		oneDayAgo.setDate(oneDayAgo.getDate() - 1);

		const newUsersThisWeek = allUsers.filter(
			(user) => new Date(user.created_at) > oneWeekAgo
		).length;
		const newUsersToday = allUsers.filter(
			(user) => new Date(user.created_at) > oneDayAgo
		).length;

		// Get feedback stats
		const allFeedback = await feedbackService.getAllFeedback();
		const totalFeedback = allFeedback.length;
		const pendingFeedback = allFeedback.filter((f) => f.status === 'pending').length;
		const resolvedFeedback = allFeedback.filter((f) => f.status === 'resolved').length;
		const newFeedbackThisWeek = allFeedback.filter(
			(f) => new Date(f.created_at) > oneWeekAgo
		).length;

		// Get LLM stats from token monitor
		const llmStats = await tokenMonitorService.getUsageStats();

		// Mock data for collections and words (TODO: implement real data)
		const mockCollections = {
			total: 3420,
			public: 2100,
			private: 1320,
			createdThisWeek: 67,
			averageWordsPerCollection: 13.4,
		};

		const mockWords = {
			total: 45600,
			byLanguage: {
				EN: 28400,
				VI: 17200,
			},
			createdThisWeek: 892,
			averageDefinitionsPerWord: 2.3,
		};

		// Mock practice stats (TODO: implement real data)
		const mockPractice = {
			totalSessions: 15600,
			activeUsersToday: 234,
			averageSessionDuration: 12.5,
			completionRate: 0.78,
		};

		// Mock cache stats (TODO: get from cache service)
		const mockCache = {
			hitRate: 0.87,
			missRate: 0.13,
			totalKeys: 45600,
			memoryUsage: 256 * 1024 * 1024, // 256MB
			evictions: 1250,
			averageResponseTime: 12,
			topCachedResources: [
				{ resource: 'word_definitions', hits: 125000, hitRate: 0.92 },
				{ resource: 'collection_data', hits: 89000, hitRate: 0.85 },
				{ resource: 'user_sessions', hits: 67000, hitRate: 0.78 },
			],
		};

		// Mock system stats (TODO: get from monitoring)
		const mockSystem = {
			apiResponseTime: 145,
			errorRate: 0.002,
			uptime: 99.8,
			memoryUsage: 78.5,
			cpuUsage: 23.4,
			diskUsage: 45.2,
			cacheHitRate: mockCache.hitRate, // Add cache hit rate to system stats
		};

		return {
			users: {
				total: totalUsers,
				active: activeUsers,
				admins: adminUsers,
				newThisWeek: newUsersThisWeek,
				newToday: newUsersToday,
				disabled: disabledUsers,
			},
			collections: mockCollections,
			words: mockWords,
			feedback: {
				total: totalFeedback,
				pending: pendingFeedback,
				resolved: resolvedFeedback,
				newThisWeek: newFeedbackThisWeek,
			},
			practice: mockPractice,
			llm: {
				totalRequests: llmStats.requestCount || 89500,
				totalTokensUsed: llmStats.totalTokens || 12500000,
				totalCost: llmStats.totalCost || 187.45,
				requestsToday: 1250, // TODO: implement daily stats
				tokensToday: 175000, // TODO: implement daily stats
				costToday: 2.63, // TODO: implement daily stats
				averageResponseTime: 850, // TODO: implement response time tracking
				errorRate: 0.012, // TODO: implement error rate tracking
				topModels: [
					{ model: 'gpt-4o-mini', usage: 67500, cost: 135.2 },
					{ model: 'gemini-1.5-flash', usage: 22000, cost: 52.25 },
					{ model: 'gpt-3.5-turbo', usage: 0, cost: 0 },
				],
			},
			cache: mockCache,
			system: mockSystem,
		};
	}

	async getSystemHealth(): Promise<{ status: string; details: Record<string, any> }> {
		try {
			const healthChecks = {
				database: 'unknown',
				cache: 'unknown',
				ai_services: 'unknown',
			};

			let overallStatus = 'healthy';

			// Check database connection
			try {
				const userService = this.getUserService();
				await userService.getUsersCount(); // Simple query to test DB
				healthChecks.database = 'connected';
			} catch (error) {
				healthChecks.database = 'error';
				overallStatus = 'error';
			}

			// Check cache service
			try {
				const cacheService = await this.getCacheService();
				// Try to set and get a test value
				await cacheService.set('health_check', 'test', 10);
				const testValue = await cacheService.get('health_check');
				healthChecks.cache = testValue === 'test' ? 'operational' : 'warning';
			} catch (error) {
				healthChecks.cache = 'error';
				if (overallStatus !== 'error') overallStatus = 'warning';
			}

			// Check AI services (simplified)
			try {
				const tokenMonitorService = this.getTokenMonitorService();
				const stats = tokenMonitorService.getUsageStats();
				healthChecks.ai_services = 'operational';
			} catch (error) {
				healthChecks.ai_services = 'warning';
				if (overallStatus === 'healthy') overallStatus = 'warning';
			}

			return {
				status: overallStatus,
				details: healthChecks,
			};
		} catch (error) {
			return {
				status: 'error',
				details: {
					database: 'error',
					cache: 'error',
					ai_services: 'error',
					error: error instanceof Error ? error.message : 'Unknown error',
				},
			};
		}
	}

	async getAllUsers(
		page: number = 1,
		limit: number = 50,
		search?: string,
		filters?: {
			provider?: Provider;
			status?: 'active' | 'disabled';
			role?: Role;
			sortBy?: 'created_at' | 'username';
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<{
		users: UserWithDetail[];
		total: number;
		page: number;
		limit: number;
	}> {
		const userService = this.getUserService();
		const offset = (page - 1) * limit;

		const users = await userService.getAllUsersWithFilters(limit, offset, search, filters);
		const total = await userService.getUsersCountWithFilters(search, filters);

		return {
			users,
			total,
			page,
			limit,
		};
	}

	async getUserById(userId: string): Promise<UserWithDetail | null> {
		return await this.getUserService().getUserById(userId);
	}

	async getUsersCount(search?: string): Promise<number> {
		return await this.getUserService().getUsersCount();
	}

	async updateUserRole(
		userId: string,
		role: Role,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		return await this.getUserService().updateUserRole(
			userId,
			role,
			adminId,
			ipAddress,
			userAgent
		);
	}

	async disableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		return await this.getUserService().disableUser(userId, adminId, ipAddress, userAgent);
	}

	async enableUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		return await this.getUserService().enableUser(userId, adminId, ipAddress, userAgent);
	}

	async createAdminUser(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		return await this.getUserService().createAdminUser(data, adminId, ipAddress, userAgent);
	}

	async createUser(
		data: {
			provider: Provider;
			provider_id: string;
			email?: string;
			name?: string;
			role: Role;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		return await this.getUserService().createUser(data, ipAddress, userAgent);
	}

	async createUserWithPassword(
		data: {
			username: string;
			password_hash: string;
			provider: Provider;
			provider_id: string;
			role: Role;
		},
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<UserWithDetail> {
		return await this.getUserService().createUserWithPassword(data, ipAddress, userAgent);
	}

	async deleteUser(
		userId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void> {
		// Get user to check if exists and get related data for audit logging
		const user = await this.getUserService().getUserById(userId);
		if (!user) {
			throw new Error('User not found');
		}

		// Get collections count for audit logging before deletion
		const collectionService = this.getCollectionService();
		const userCollections = await collectionService.getUserCollections(userId);
		const collectionsCount = userCollections.length;

		// Delete the user - cascade deletes will handle all related data automatically
		await this.getUserService().deleteUser(userId);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.ADMIN_USER_DELETED,
				userId,
				adminId,
				{
					username: user.username,
					provider: user.provider,
					role: user.role,
					collections_deleted: collectionsCount,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for user deletion:', error);
		}
	}

	async bulkDeleteUsers(
		userIds: string[],
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<{ success: number; failed: number; errors: string[] }> {
		const results = { success: 0, failed: 0, errors: [] as string[] };
		const adminCount = await this.getAdminCount();

		for (const userId of userIds) {
			try {
				// Check if user exists
				const user = await this.getUserService().getUserById(userId);
				if (!user) {
					results.failed++;
					results.errors.push(`User ${userId} not found`);
					continue;
				}

				// Prevent deleting the last admin
				if (user.role === Role.ADMIN && adminCount <= 1) {
					results.failed++;
					results.errors.push(
						`Cannot delete the last admin user: ${user.username || userId}`
					);
					continue;
				}

				// Delete user
				await this.deleteUser(userId, adminId, ipAddress, userAgent);
				results.success++;
			} catch (error) {
				results.failed++;
				results.errors.push(
					`Failed to delete user ${userId}: ${
						error instanceof Error ? error.message : 'Unknown error'
					}`
				);
			}
		}

		return results;
	}

	async updateUserProfile(
		userId: string,
		data: { email?: string; name?: string }
	): Promise<UserWithDetail> {
		return await this.getUserService().updateUserProfile(userId, data);
	}

	async changeUserPassword(
		userId: string,
		passwordHash: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void> {
		await this.getUserService().changeUserPassword(userId, passwordHash);

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logUserEvent(
				AUDIT_ACTIONS.PASSWORD_CHANGED,
				userId,
				adminId,
				{
					admin_action: true,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for password change:', error);
		}
	}

	async getAdminCount(): Promise<number> {
		return await this.getUserService().getAdminCount();
	}

	// Feedback Management
	async getAllFeedback(
		page: number = 1,
		limit: number = 50,
		search?: string,
		status?: string
	): Promise<{
		feedbacks: any[];
		total: number;
		page: number;
		limit: number;
	}> {
		const feedbackService = this.getFeedbackService();
		const offset = (page - 1) * limit;

		// Get all feedback with user information
		const allFeedback = await feedbackService.getAllFeedback();

		// Apply filters
		let filteredFeedback = allFeedback;

		if (search) {
			filteredFeedback = filteredFeedback.filter(
				(feedback) =>
					feedback.message.toLowerCase().includes(search.toLowerCase()) ||
					(feedback.user?.username &&
						feedback.user.username.toLowerCase().includes(search.toLowerCase()))
			);
		}

		if (status && status !== 'all') {
			filteredFeedback = filteredFeedback.filter((feedback) => feedback.status === status);
		}

		const total = filteredFeedback.length;
		const feedbacks = filteredFeedback.slice(offset, offset + limit);

		return {
			feedbacks,
			total,
			page,
			limit,
		};
	}

	async deleteFeedback(
		feedbackId: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void> {
		const feedbackRepository = this.getFeedbackRepository();

		// Check if feedback exists
		const feedback = await feedbackRepository.findById(feedbackId);
		if (!feedback) {
			throw new Error('Feedback not found');
		}

		// Delete the feedback
		await feedbackRepository.delete({ id: feedbackId });

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logFeedbackEvent(
				AUDIT_ACTIONS.FEEDBACK_DELETED,
				feedbackId,
				adminId,
				{
					message_preview: feedback.message.substring(0, 100),
					status: feedback.status,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for feedback deletion:', error);
		}
	}

	async bulkDeleteFeedback(
		feedbackIds: string[],
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<{ success: number; failed: number; errors: string[] }> {
		const results = { success: 0, failed: 0, errors: [] as string[] };

		for (const feedbackId of feedbackIds) {
			try {
				await this.deleteFeedback(feedbackId, adminId, ipAddress, userAgent);
				results.success++;
			} catch (error) {
				results.failed++;
				results.errors.push(
					`Failed to delete feedback ${feedbackId}: ${
						error instanceof Error ? error.message : 'Unknown error'
					}`
				);
			}
		}

		return results;
	}

	async updateFeedbackStatus(
		feedbackId: string,
		status: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<any> {
		const feedbackRepository = this.getFeedbackRepository();

		// Check if feedback exists
		const feedback = await feedbackRepository.findById(feedbackId);
		if (!feedback) {
			throw new Error('Feedback not found');
		}

		// Update the feedback status
		const updatedFeedback = await feedbackRepository.update(feedbackId, { status });

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logFeedbackEvent(
				AUDIT_ACTIONS.FEEDBACK_STATUS_UPDATED,
				feedbackId,
				adminId,
				{
					old_status: feedback.status,
					new_status: status,
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for feedback status update:', error);
		}

		return updatedFeedback;
	}

	async clearCache(
		cacheType?: string,
		adminId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<{ success: boolean; message: string }> {
		try {
			const cacheService = await this.getCacheService();
			// TODO: Implement cache clearing by type
			// For now, clear all cache

			// Log audit event
			try {
				const auditHelper = this.getAuditHelper();
				await auditHelper.logCacheEvent(
					AUDIT_ACTIONS.ADMIN_CACHE_CLEARED,
					cacheType || 'all',
					undefined, // no specific user
					{
						cache_type: cacheType || 'all',
						admin_action: true,
					},
					{ ip_address: ipAddress, user_agent: userAgent }
				);
			} catch (error) {
				console.error('Failed to log audit event for cache clearing:', error);
			}

			return {
				success: true,
				message: 'Cache cleared successfully',
			};
		} catch (error) {
			return {
				success: false,
				message: `Failed to clear cache: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`,
			};
		}
	}

	async getTokenUsageStats(): Promise<any> {
		const tokenMonitorService = this.getTokenMonitorService();
		return await tokenMonitorService.getUsageStats();
	}

	async getRecentActivity(limit: number = 50): Promise<any[]> {
		// TODO: Implement activity logging and retrieval
		return [];
	}

	async getErrorLogs(limit: number = 50): Promise<any[]> {
		// For now, return mock error logs
		// In production, this would fetch from a logging service or database
		const mockErrors = [
			{
				id: '1',
				timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
				level: 'error',
				message: 'Database connection timeout',
				source: 'UserService',
				details: 'Connection to database timed out after 30 seconds',
			},
			{
				id: '2',
				timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
				level: 'warning',
				message: 'High memory usage detected',
				source: 'SystemMonitor',
				details: 'Memory usage exceeded 80% threshold',
			},
			{
				id: '3',
				timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
				level: 'error',
				message: 'AI service rate limit exceeded',
				source: 'LLMService',
				details: 'OpenAI API rate limit exceeded, requests throttled',
			},
		];

		// Return only recent errors (last 24 hours) and limit results
		const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
		return mockErrors
			.filter((error) => error.timestamp > oneDayAgo)
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
			.slice(0, limit);
	}
}
