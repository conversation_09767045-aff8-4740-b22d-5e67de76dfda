import { Cefr<PERSON>ord, Prisma, PrismaClient } from '@prisma/client';
import { BaseRepository, BaseRepositoryImpl } from './base.repository';

export interface CefrWordRepository extends BaseRepository<CefrWord> {
	findByTerm(term: string): Promise<CefrWord | null>;
	findByLevel(level: string, limit?: number): Promise<CefrWord[]>;
	searchWords(term: string, limit?: number): Promise<CefrWord[]>;
	findByTerms(terms: string[]): Promise<CefrWord[]>;
	getAllLevels(): Promise<string[]>;
	getWordCountByLevel(): Promise<{ level: string; count: number }[]>;
	findRandomWordsByLevel(level: string, limit?: number): Promise<CefrWord[]>;
}

export class CefrWordRepositoryImpl
	extends BaseRepositoryImpl<CefrWord>
	implements CefrWordRepository
{
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.cefrWord);
	}

	override async findById(id: string): Promise<CefrWord | null> {
		const cefrWord = await this.prisma.cefrWord.findUnique({
			where: { id },
		});
		return cefrWord;
	}

	override async findOne(query: Prisma.CefrWordWhereInput): Promise<CefrWord | null> {
		const cefrWord = await this.prisma.cefrWord.findFirst({
			where: query,
		});
		return cefrWord;
	}

	override async find(query: Prisma.CefrWordWhereInput, limit?: number): Promise<CefrWord[]> {
		const cefrWords = await this.prisma.cefrWord.findMany({
			where: query,
			take: limit,
		});
		return cefrWords;
	}

	override async create(data: Prisma.CefrWordCreateInput): Promise<CefrWord> {
		if (!data.term || !data.level) {
			throw new Error('Term and level are required');
		}

		const cefrWord = await this.prisma.cefrWord.create({
			data,
		});
		return cefrWord;
	}

	override async update(id: string, data: Prisma.CefrWordUpdateInput): Promise<CefrWord> {
		const cefrWord = await this.prisma.cefrWord.update({
			where: { id },
			data,
		});
		return cefrWord;
	}

	override async delete(query: Record<string, unknown>): Promise<void> {
		await this.prisma.cefrWord.deleteMany({
			where: query,
		});
	}

	async findByTerm(term: string): Promise<CefrWord | null> {
		const cefrWord = await this.prisma.cefrWord.findFirst({
			where: {
				term: {
					equals: term,
					mode: 'insensitive',
				},
			},
		});
		return cefrWord;
	}

	async findByLevel(level: string, limit = 50): Promise<CefrWord[]> {
		const cefrWords = await this.prisma.cefrWord.findMany({
			where: {
				level,
			},
			take: limit,
			orderBy: {
				term: 'asc',
			},
		});
		return cefrWords;
	}

	async searchWords(term: string, limit = 10): Promise<CefrWord[]> {
		const cefrWords = await this.prisma.cefrWord.findMany({
			where: {
				term: {
					contains: term,
					mode: 'insensitive',
				},
			},
			take: limit,
			orderBy: {
				term: 'asc',
			},
		});
		return cefrWords;
	}

	async findByTerms(terms: string[]): Promise<CefrWord[]> {
		const cefrWords = await this.prisma.cefrWord.findMany({
			where: {
				term: {
					in: terms,
					mode: 'insensitive',
				},
			},
			orderBy: {
				term: 'asc',
			},
		});
		return cefrWords;
	}

	async getAllLevels(): Promise<string[]> {
		const result = await this.prisma.cefrWord.findMany({
			select: {
				level: true,
			},
			distinct: ['level'],
			orderBy: {
				level: 'asc',
			},
		});
		return result.map((item) => item.level);
	}

	async getWordCountByLevel(): Promise<{ level: string; count: number }[]> {
		const result = await this.prisma.cefrWord.groupBy({
			by: ['level'],
			_count: {
				id: true,
			},
			orderBy: {
				level: 'asc',
			},
		});
		return result.map((item) => ({
			level: item.level,
			count: item._count.id,
		}));
	}

	async findRandomWordsByLevel(level: string, limit = 10): Promise<CefrWord[]> {
		// Get total count for the level
		const totalCount = await this.prisma.cefrWord.count({
			where: { level },
		});

		if (totalCount === 0) {
			return [];
		}

		// Generate random skip value
		const skip = Math.floor(Math.random() * Math.max(0, totalCount - limit));

		const cefrWords = await this.prisma.cefrWord.findMany({
			where: { level },
			skip,
			take: limit,
			orderBy: {
				term: 'asc',
			},
		});
		return cefrWords;
	}
}
