import { Prisma } from '@prisma/client';
import { z } from 'zod';

// CEFR levels enum
export enum CefrLevel {
	A1 = 'A1',
	A2 = 'A2',
	B1 = 'B1',
	B2 = 'B2',
	C1 = 'C1',
	C2 = 'C2',
}

// Zod schema for CEFR word validation
export const CefrWordSchema = z.object({
	id: z.string().uuid().optional(),
	term: z.string().min(1, 'Term is required'),
	level: z.nativeEnum(CefrLevel, {
		errorMap: () => ({ message: 'Invalid CEFR level' }),
	}),
	created_at: z.date().optional(),
	updated_at: z.date().optional(),
});

export const CreateCefrWordSchema = CefrWordSchema.omit({
	id: true,
	created_at: true,
	updated_at: true,
});

export const UpdateCefrWordSchema = CefrWordSchema.partial().omit({
	id: true,
	created_at: true,
	updated_at: true,
});

// Type definitions
export type CefrWord = Prisma.CefrWordGetPayload<{}>;

export type CreateCefrWordInput = z.infer<typeof CreateCefrWordSchema>;

export type UpdateCefrWordInput = z.infer<typeof UpdateCefrWordSchema>;

// CEFR word with additional metadata
export interface CefrWordWithMetadata extends CefrWord {
	isKnown?: boolean;
	difficulty_score?: number;
	frequency_rank?: number;
}

// CEFR level statistics
export interface CefrLevelStats {
	level: CefrLevel;
	total_words: number;
	known_words?: number;
	mastery_percentage?: number;
}

// Search and filter options
export interface CefrWordSearchOptions {
	term?: string;
	level?: CefrLevel;
	levels?: CefrLevel[];
	limit?: number;
	offset?: number;
	sortBy?: 'term' | 'level' | 'created_at';
	sortOrder?: 'asc' | 'desc';
}

// Bulk operations
export interface BulkCefrWordOperation {
	words: CreateCefrWordInput[];
	skipDuplicates?: boolean;
}

export interface CefrWordImportResult {
	success: number;
	failed: number;
	duplicates: number;
	errors: string[];
}

// CEFR word learning progress
export interface CefrWordProgress {
	word_id: string;
	user_id: string;
	mastery_level: number; // 0-100
	last_reviewed: Date;
	review_count: number;
	correct_answers: number;
	total_attempts: number;
}

// CEFR vocabulary recommendations
export interface CefrVocabularyRecommendation {
	current_level: CefrLevel;
	recommended_words: CefrWord[];
	next_level_preview: CefrWord[];
	mastery_gaps: CefrWord[];
}
