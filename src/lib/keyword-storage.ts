import { KeywordWithDetail } from '@/models';
import * as keywordIndexedDB from './indexed-db/keyword-indexed-db';
import { forceRecreateDatabase } from './indexed-db';

// Legacy localStorage keys for migration
const KEYWORDS_STORAGE_KEY = 'vocab-keywords';
const SELECTED_KEYWORDS_STORAGE_KEY = 'vocab-selected-keywords';
const KEYWORDS_SYNC_QUEUE_KEY = 'vocab-keywords-sync-queue';
const MIGRATION_COMPLETED_KEY = 'vocab-keywords-migration-completed';
const INITIAL_FETCH_COMPLETED_KEY = 'vocab-keywords-initial-fetch-completed';

export interface KeywordSyncAction {
	id: string;
	type: 'create' | 'update' | 'delete';
	data?: {
		name?: string;
		keywordId?: string;
	};
	timestamp: number;
}

export interface KeywordStorageData {
	keywords: KeywordWithDetail[];
	lastSyncTimestamp: number;
	version: number;
}

/**
 * Utility class for managing keywords in IndexedDB with background sync
 */
export class KeywordStorage {
	private static instance: KeywordStorage;
	private migrationPromise: Promise<void> | null = null;
	private initialFetchPromise: Promise<void> | null = null;

	private constructor() {
		// Start migration on first instantiation
		this.migrationPromise = this.migrateFromLocalStorage();
	}

	static getInstance(): KeywordStorage {
		if (!KeywordStorage.instance) {
			KeywordStorage.instance = new KeywordStorage();
		}
		return KeywordStorage.instance;
	}

	/**
	 * Ensure migration is completed before any operation
	 */
	private async ensureMigration(): Promise<void> {
		if (this.migrationPromise) {
			await this.migrationPromise;
		}
	}

	/**
	 * Ensure initial data fetch from server is completed
	 */
	private async ensureInitialFetch(): Promise<void> {
		if (!this.initialFetchPromise) {
			this.initialFetchPromise = this.initialFetchFromServer();
		}
		return this.initialFetchPromise;
	}

	/**
	 * Check if initial fetch has been completed
	 */
	private isInitialFetchCompleted(): boolean {
		if (typeof window === 'undefined') return false;
		return localStorage.getItem(INITIAL_FETCH_COMPLETED_KEY) === 'true';
	}

	/**
	 * Mark initial fetch as completed
	 */
	private markInitialFetchCompleted(): void {
		if (typeof window !== 'undefined') {
			localStorage.setItem(INITIAL_FETCH_COMPLETED_KEY, 'true');
		}
	}

	/**
	 * Migrate data from localStorage to IndexedDB (one-time operation)
	 */
	private async migrateFromLocalStorage(): Promise<void> {
		try {
			// Skip migration on server-side (localStorage not available)
			if (typeof window === 'undefined') {
				return;
			}

			// Check if migration already completed
			const migrationCompleted = localStorage.getItem(MIGRATION_COMPLETED_KEY);
			if (migrationCompleted === 'true') {
				return;
			}

			console.log('Starting keyword migration from localStorage to IndexedDB...');

			// Migrate keywords
			const keywordsData = localStorage.getItem(KEYWORDS_STORAGE_KEY);
			if (keywordsData) {
				try {
					const parsed: KeywordStorageData = JSON.parse(keywordsData);
					if (parsed.keywords && parsed.keywords.length > 0) {
						await keywordIndexedDB.saveKeywords(parsed.keywords);
						console.log(`Migrated ${parsed.keywords.length} keywords to IndexedDB`);
					}
				} catch (error) {
					console.error('Failed to migrate keywords:', error);
				}
			}

			// Migrate selected keywords
			const selectedData = localStorage.getItem(SELECTED_KEYWORDS_STORAGE_KEY);
			if (selectedData) {
				try {
					const selectedIds: string[] = JSON.parse(selectedData);
					if (selectedIds.length > 0) {
						await keywordIndexedDB.saveSelectedKeywords(selectedIds);
						console.log(
							`Migrated ${selectedIds.length} selected keywords to IndexedDB`
						);
					}
				} catch (error) {
					console.error('Failed to migrate selected keywords:', error);
				}
			}

			// Migrate sync queue
			const syncQueueData = localStorage.getItem(KEYWORDS_SYNC_QUEUE_KEY);
			if (syncQueueData) {
				try {
					const syncQueue: KeywordSyncAction[] = JSON.parse(syncQueueData);
					for (const action of syncQueue) {
						await keywordIndexedDB.addToSyncQueue(action);
					}
					console.log(`Migrated ${syncQueue.length} sync actions to IndexedDB`);
				} catch (error) {
					console.error('Failed to migrate sync queue:', error);
				}
			}

			// Mark migration as completed
			localStorage.setItem(MIGRATION_COMPLETED_KEY, 'true');

			// Clean up old localStorage data
			localStorage.removeItem(KEYWORDS_STORAGE_KEY);
			localStorage.removeItem(SELECTED_KEYWORDS_STORAGE_KEY);
			localStorage.removeItem(KEYWORDS_SYNC_QUEUE_KEY);

			console.log('Keyword migration completed successfully');
		} catch (error) {
			console.error('Failed to migrate keywords from localStorage:', error);
		}
	}

	/**
	 * Initial fetch of keywords from server (one-time operation)
	 */
	private async initialFetchFromServer(): Promise<void> {
		try {
			// Skip on server-side
			if (typeof window === 'undefined') {
				return;
			}

			// Skip if already completed
			if (this.isInitialFetchCompleted()) {
				return;
			}

			console.log('Starting initial fetch of keywords from server...');

			// Fetch keywords from server
			const response = await fetch('/api/keywords');
			if (!response.ok) {
				throw new Error(`Failed to fetch keywords: ${response.status}`);
			}

			const serverKeywords: KeywordWithDetail[] = await response.json();

			// Save to IndexedDB
			await keywordIndexedDB.saveKeywords(serverKeywords);

			// Mark as completed
			this.markInitialFetchCompleted();

			console.log(
				`Initial fetch completed: ${serverKeywords.length} keywords loaded from server`
			);
		} catch (error) {
			console.error('Failed to fetch initial keywords from server:', error);
			// Don't throw error to prevent app from breaking
			// App can still work with local data
		}
	}

	/**
	 * Get keywords from IndexedDB
	 */
	async getKeywords(): Promise<KeywordWithDetail[]> {
		await this.ensureMigration();
		await this.ensureInitialFetch();
		return keywordIndexedDB.getKeywords();
	}

	/**
	 * Save keywords to IndexedDB
	 */
	async saveKeywords(keywords: KeywordWithDetail[]): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.saveKeywords(keywords);
	}

	/**
	 * Get selected keywords from IndexedDB
	 */
	async getSelectedKeywords(): Promise<string[]> {
		await this.ensureMigration();
		return keywordIndexedDB.getSelectedKeywords();
	}

	/**
	 * Save selected keywords to IndexedDB
	 */
	async saveSelectedKeywords(selectedIds: string[]): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.saveSelectedKeywords(selectedIds);
	}

	/**
	 * Add keyword locally (optimistic update)
	 */
	async addKeywordLocally(keyword: KeywordWithDetail): Promise<void> {
		await this.ensureMigration();
		const keywords = await this.getKeywords();
		const updatedKeywords = [...keywords, keyword];
		return this.saveKeywords(updatedKeywords);
	}

	/**
	 * Add keyword with sync queue (optimistic update + sync)
	 */
	async addKeywordWithSync(name: string, userId: string): Promise<KeywordWithDetail> {
		await this.ensureMigration();

		// Generate temporary ID for optimistic update
		const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

		// Create keyword object
		const keyword: KeywordWithDetail = {
			id: tempId,
			content: name.trim(),
			user_id: userId,
		};

		// Add to IndexedDB immediately (optimistic update)
		await this.addKeywordLocally(keyword);

		// Add to sync queue
		const actionId = this.generateActionId();
		await this.addToSyncQueue({
			id: actionId,
			type: 'create',
			data: { name: name.trim() },
		});

		return keyword;
	}

	/**
	 * Update keyword locally (optimistic update)
	 */
	async updateKeywordLocally(id: string, updates: Partial<KeywordWithDetail>): Promise<void> {
		await this.ensureMigration();
		const keywords = await this.getKeywords();
		const updatedKeywords = keywords.map((keyword) =>
			keyword.id === id ? { ...keyword, ...updates } : keyword
		);
		return this.saveKeywords(updatedKeywords);
	}

	/**
	 * Update keyword with sync queue (optimistic update + sync)
	 */
	async updateKeywordWithSync(id: string, name: string): Promise<void> {
		await this.ensureMigration();

		// Update locally first (optimistic update)
		await this.updateKeywordLocally(id, { content: name.trim() });

		// Clean up any conflicting sync actions for this keyword
		await this.cleanupConflictingActions(id);

		// Add to sync queue
		const actionId = this.generateActionId();
		await this.addToSyncQueue({
			id: actionId,
			type: 'update',
			data: { keywordId: id, name: name.trim() },
		});
	}

	/**
	 * Delete keyword locally (optimistic update)
	 */
	async deleteKeywordLocally(id: string): Promise<void> {
		await this.ensureMigration();
		const keywords = await this.getKeywords();
		const updatedKeywords = keywords.filter((keyword) => keyword.id !== id);
		await this.saveKeywords(updatedKeywords);

		// Also remove from selected keywords
		const selectedKeywords = await this.getSelectedKeywords();
		const updatedSelected = selectedKeywords.filter((keywordId) => keywordId !== id);
		return this.saveSelectedKeywords(updatedSelected);
	}

	/**
	 * Delete keyword with sync queue (optimistic update + sync)
	 */
	async deleteKeywordWithSync(id: string): Promise<void> {
		await this.ensureMigration();

		// Delete locally first (optimistic update)
		await this.deleteKeywordLocally(id);

		// Clean up any conflicting sync actions for this keyword
		await this.cleanupConflictingActions(id);

		// Add to sync queue (only if not a temporary keyword)
		if (!id.startsWith('temp_')) {
			const actionId = this.generateActionId();
			await this.addToSyncQueue({
				id: actionId,
				type: 'delete',
				data: { keywordId: id },
			});
		}
	}

	/**
	 * Get sync queue from IndexedDB
	 */
	async getSyncQueue(): Promise<KeywordSyncAction[]> {
		await this.ensureMigration();
		return keywordIndexedDB.getSyncQueue();
	}

	/**
	 * Add action to sync queue
	 */
	async addToSyncQueue(action: Omit<KeywordSyncAction, 'timestamp'>): Promise<void> {
		await this.ensureMigration();
		const newAction: KeywordSyncAction = {
			...action,
			timestamp: Date.now(),
		};
		return keywordIndexedDB.addToSyncQueue(newAction);
	}

	/**
	 * Remove action from sync queue
	 */
	async removeFromSyncQueue(actionId: string): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.removeFromSyncQueue(actionId);
	}

	/**
	 * Clear sync queue
	 */
	async clearSyncQueue(): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.clearSyncQueue();
	}

	/**
	 * Clear all keyword data from IndexedDB
	 */
	async clearAll(): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.clearAllKeywordData();
	}

	/**
	 * Reset IndexedDB in case of corruption or version issues
	 */
	async resetIndexedDB(): Promise<void> {
		try {
			// Force recreate the database with all object stores
			await forceRecreateDatabase();

			// Reset migration flag to allow re-migration if needed
			if (typeof window !== 'undefined') {
				localStorage.removeItem('vocab-keywords-migration-completed');
			}

			// Reinitialize migration
			this.migrationPromise = this.migrateFromLocalStorage();
			await this.migrationPromise;

			console.log('IndexedDB reset completed successfully');
		} catch (error) {
			console.error('Failed to reset IndexedDB:', error);
			throw error;
		}
	}

	/**
	 * Generate unique ID for sync actions
	 */
	generateActionId(): string {
		return keywordIndexedDB.generateActionId();
	}

	/**
	 * Clean up conflicting sync actions for a keyword
	 * This removes any existing actions for the same keyword to prevent conflicts
	 */
	async cleanupConflictingActions(keywordId: string): Promise<void> {
		await this.ensureMigration();
		const queue = await this.getSyncQueue();

		// Find all actions related to this keyword
		const conflictingActions = queue.filter((action) => {
			// For create actions with temporary IDs, check if the action relates to this keyword
			if (action.type === 'create') {
				// If this is a temporary keyword, we might want to keep create actions
				// But if we're updating/deleting, we should remove pending creates
				return false; // Don't remove create actions automatically
			}
			// For update/delete actions, check if the keywordId matches
			if (
				(action.type === 'update' || action.type === 'delete') &&
				action.data?.keywordId === keywordId
			) {
				return true;
			}
			return false;
		});

		// Remove all conflicting actions
		for (const action of conflictingActions) {
			await this.removeFromSyncQueue(action.id);
		}

		if (conflictingActions.length > 0) {
			console.log(
				`Cleaned up ${conflictingActions.length} conflicting sync actions for keyword ${keywordId}`
			);
		}
	}

	/**
	 * Clean up all sync actions for a temporary keyword that was successfully created on server
	 */
	async cleanupTemporaryKeywordActions(tempId: string, serverId: string): Promise<void> {
		await this.ensureMigration();
		const queue = await this.getSyncQueue();

		// Find create actions for the temporary keyword
		const tempActions = queue.filter((action) => {
			return action.type === 'create' && action.id.includes(tempId.split('_')[1]);
		});

		// Remove temporary actions
		for (const action of tempActions) {
			await this.removeFromSyncQueue(action.id);
		}

		if (tempActions.length > 0) {
			console.log(
				`Cleaned up ${tempActions.length} temporary sync actions for keyword ${tempId} -> ${serverId}`
			);
		}
	}
}

export const keywordStorage = KeywordStorage.getInstance();
