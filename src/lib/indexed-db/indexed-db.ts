export const DB_NAME = 'vocab-db';
export const DB_VERSION = 3; // Incremented to fix object store creation issue

// Centralized store configurations for all features
const ALL_STORE_CONFIGS = [
	// Collection stores
	{ storeName: 'collections', options: { keyPath: 'id' } },
	// Keyword stores
	{ storeName: 'keywords', options: { keyPath: 'id' } },
	{ storeName: 'selected-keywords', options: { keyPath: 'id' } },
	{ storeName: 'keyword-sync-queue', options: { keyPath: 'id' } },
];

/**
 * A generic manager for interacting with IndexedDB.
 */
export class IndexedDBManager {
	private dbName: string;
	private dbVersion: number;
	private storeConfigs: { storeName: string; options?: IDBObjectStoreParameters }[];
	private db: IDBDatabase | null = null;

	constructor(
		dbName: string,
		dbVersion: number,
		storeConfigs: { storeName: string; options?: IDBObjectStoreParameters }[]
	) {
		this.dbName = dbName;
		this.dbVersion = dbVersion;
		this.storeConfigs = storeConfigs;
	}

	/**
	 * Opens the IndexedDB database and creates object stores if necessary.
	 * @returns A Promise that resolves with the database instance.
	 */
	private async openDB(): Promise<IDBDatabase> {
		if (this.db) {
			return Promise.resolve(this.db);
		}

		return new Promise((resolve, reject) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				this.storeConfigs.forEach((config) => {
					if (!db.objectStoreNames.contains(config.storeName)) {
						db.createObjectStore(config.storeName, config.options);
					}
				});
			};

			request.onsuccess = (event) => {
				this.db = (event.target as IDBOpenDBRequest).result;
				resolve(this.db!);
			};

			request.onerror = (event) => {
				reject((event.target as IDBOpenDBRequest).error);
			};
		});
	}

	/**
	 * Gets the IndexedDB database instance, opening it if necessary.
	 * @returns A Promise that resolves with the database instance.
	 */
	public async getDB(): Promise<IDBDatabase> {
		return this.openDB();
	}

	/**
	 * Ensures that the specified object store exists, recreating the database if necessary
	 * @param storeName The name of the object store to check
	 * @returns A Promise that resolves with the database instance
	 */
	private async ensureObjectStore(storeName: string): Promise<IDBDatabase> {
		const db = await this.openDB();

		// Check if the object store exists
		if (!db.objectStoreNames.contains(storeName)) {
			console.warn(
				`Object store '${storeName}' not found. This might be due to a database version mismatch. Attempting to recreate database...`
			);

			// Close current database and force recreation
			this.db?.close();
			this.db = null;

			// Delete and recreate the database
			await this.deleteDatabase();
			const newDb = await this.openDB();

			// If store still doesn't exist after recreation, throw error
			if (!newDb.objectStoreNames.contains(storeName)) {
				throw new Error(
					`Object store '${storeName}' still not found after database recreation.`
				);
			}

			return newDb;
		}

		return db;
	}

	/**
	 * Puts (adds or updates) data into a specific object store.
	 * @param storeName The name of the object store.
	 * @param data The data object to put.
	 */
	public async put<T>(storeName: string, data: T): Promise<void> {
		const db = await this.ensureObjectStore(storeName);
		return new Promise((resolve, reject) => {
			try {
				const transaction = db.transaction([storeName], 'readwrite');
				const store = transaction.objectStore(storeName);
				const request = store.put(data);

				request.onsuccess = () => resolve();
				request.onerror = (event) => reject((event.target as IDBRequest).error);
			} catch (error) {
				console.error(`Failed to create transaction for store '${storeName}':`, error);
				reject(error);
			}
		});
	}

	/**
	 * Gets data from a specific object store by key.
	 * @param storeName The name of the object store.
	 * @param key The key of the data to retrieve.
	 * @returns A Promise that resolves with the data object, or null if not found.
	 */
	public async get<T>(storeName: string, key: IDBValidKey): Promise<T | null> {
		const db = await this.ensureObjectStore(storeName);
		return new Promise((resolve, reject) => {
			try {
				const transaction = db.transaction([storeName], 'readonly');
				const store = transaction.objectStore(storeName);
				const request = store.get(key);

				request.onsuccess = (event) => {
					resolve((event.target as IDBRequest).result || null);
				};
				request.onerror = (event) => reject((event.target as IDBRequest).error);
			} catch (error) {
				console.error(`Failed to create transaction for store '${storeName}':`, error);
				resolve(null);
			}
		});
	}

	/**
	 * Gets all data from a specific object store.
	 * @param storeName The name of the object store.
	 * @returns A Promise that resolves with an array of all data objects.
	 */
	public async getAll<T>(storeName: string): Promise<T[]> {
		try {
			const db = await this.ensureObjectStore(storeName);
			return new Promise((resolve, reject) => {
				try {
					const transaction = db.transaction([storeName], 'readonly');
					const store = transaction.objectStore(storeName);
					const request = store.getAll();

					request.onsuccess = (event) => {
						resolve((event.target as IDBRequest).result as T[]);
					};
					request.onerror = (event) => reject((event.target as IDBRequest).error);
				} catch (error) {
					console.error(`Failed to create transaction for store '${storeName}':`, error);
					resolve([]);
				}
			});
		} catch (error) {
			console.error(`Failed to ensure object store '${storeName}':`, error);
			return [];
		}
	}

	/**
	 * Deletes data from a specific object store by key.
	 * @param storeName The name of the object store.
	 * @param key The key of the data to delete.
	 */
	public async delete(storeName: string, key: IDBValidKey): Promise<void> {
		const db = await this.ensureObjectStore(storeName);
		return new Promise((resolve, reject) => {
			try {
				const transaction = db.transaction([storeName], 'readwrite');
				const store = transaction.objectStore(storeName);
				const request = store.delete(key);

				request.onsuccess = () => resolve();
				request.onerror = (event) => reject((event.target as IDBRequest).error);
			} catch (error) {
				console.error(`Failed to create transaction for store '${storeName}':`, error);
				reject(error);
			}
		});
	}

	/**
	 * Clears all data from a specific object store.
	 * @param storeName The name of the object store.
	 */
	public async clear(storeName: string): Promise<void> {
		const db = await this.ensureObjectStore(storeName);
		return new Promise((resolve, reject) => {
			try {
				const transaction = db.transaction([storeName], 'readwrite');
				const store = transaction.objectStore(storeName);
				const request = store.clear();

				request.onsuccess = () => resolve();
				request.onerror = (event) => reject((event.target as IDBRequest).error);
			} catch (error) {
				console.error(`Failed to create transaction for store '${storeName}':`, error);
				reject(error);
			}
		});
	}

	/**
	 * Deletes the entire IndexedDB database
	 */
	private async deleteDatabase(): Promise<void> {
		return new Promise((resolve, reject) => {
			// Close the current database connection first
			if (this.db) {
				this.db.close();
				this.db = null;
			}

			const deleteRequest = indexedDB.deleteDatabase(this.dbName);

			deleteRequest.onsuccess = () => {
				console.log(`IndexedDB database '${this.dbName}' deleted successfully`);
				resolve();
			};

			deleteRequest.onerror = (event) => {
				console.error(
					`Failed to delete IndexedDB database '${this.dbName}':`,
					(event.target as IDBOpenDBRequest).error
				);
				reject((event.target as IDBOpenDBRequest).error);
			};

			deleteRequest.onblocked = () => {
				console.warn(
					`Delete request for database '${this.dbName}' is blocked. Trying to resolve...`
				);
				// The delete request is blocked, usually because there are open connections
				// We'll resolve anyway and let the next open attempt handle it
				setTimeout(() => resolve(), 1000);
			};
		});
	}
}

// Singleton instance to ensure all parts of the app use the same database manager
let sharedDBManager: IndexedDBManager | null = null;

/**
 * Get the shared IndexedDB manager instance with all store configurations
 */
export function getSharedDBManager(): IndexedDBManager {
	if (!sharedDBManager) {
		sharedDBManager = new IndexedDBManager(DB_NAME, DB_VERSION, ALL_STORE_CONFIGS);
	}
	return sharedDBManager;
}

/**
 * Reset the shared IndexedDB manager (useful for testing or error recovery)
 */
export function resetSharedDBManager(): void {
	if (sharedDBManager) {
		sharedDBManager = null;
	}
}

/**
 * Clear all data from IndexedDB and reset the manager
 */
export async function clearAllIndexedDBData(): Promise<void> {
	try {
		const manager = getSharedDBManager();
		const db = await manager.getDB();

		// Clear all object stores
		for (const config of ALL_STORE_CONFIGS) {
			try {
				await manager.clear(config.storeName);
			} catch (error) {
				console.warn(`Failed to clear store ${config.storeName}:`, error);
			}
		}

		console.log('All IndexedDB data cleared successfully');
	} catch (error) {
		console.error('Failed to clear IndexedDB data:', error);
		// If clearing fails, try to delete the entire database
		try {
			await deleteIndexedDB();
		} catch (deleteError) {
			console.error('Failed to delete IndexedDB:', deleteError);
		}
	}
}

/**
 * Delete the entire IndexedDB database
 */
export async function deleteIndexedDB(): Promise<void> {
	return new Promise((resolve, reject) => {
		// Close any existing connections
		if (sharedDBManager) {
			sharedDBManager = null;
		}

		const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

		deleteRequest.onsuccess = () => {
			console.log('IndexedDB database deleted successfully');
			resolve();
		};

		deleteRequest.onerror = (event) => {
			console.error(
				'Failed to delete IndexedDB database:',
				(event.target as IDBOpenDBRequest).error
			);
			reject((event.target as IDBOpenDBRequest).error);
		};

		deleteRequest.onblocked = () => {
			console.warn('IndexedDB deletion blocked - close all tabs and try again');
			// Still resolve as the deletion will complete when tabs are closed
			resolve();
		};
	});
}

/**
 * Force recreate the IndexedDB database (useful for fixing object store issues)
 */
export async function forceRecreateDatabase(): Promise<void> {
	try {
		console.log('Force recreating IndexedDB database...');

		// Reset the shared manager
		resetSharedDBManager();

		// Delete the database
		await deleteIndexedDB();

		// Create a new manager which will recreate the database with all stores
		const manager = getSharedDBManager();
		await manager.getDB();

		console.log('IndexedDB database recreated successfully');
	} catch (error) {
		console.error('Failed to force recreate IndexedDB database:', error);
		throw error;
	}
}
